"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { 
  CreditCard, 
  Search, 
  Download, 
  Eye,
  Calendar,
  DollarSign,
  CheckCircle,
  XCircle,
  Clock,
  Loader2,
  ArrowUpRight,
  ArrowDownLeft
} from "lucide-react"

interface Pagamento {
  id: number
  tipo: "entrada" | "saida"
  descricao: string
  valor: number
  metodo: string
  status: "concluido" | "pendente" | "cancelado"
  data: string
  referencia?: string
}

export default function PagamentosPage() {
  const [pagamentos, setPagamentos] = useState<Pagamento[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("todos")
  const [tipoFilter, setTipoFilter] = useState("todos")

  useEffect(() => {
    loadPagamentos()
  }, [])

  const loadPagamentos = async () => {
    try {
      setLoading(true)
      
      // Simular dados dos pagamentos
      const mockPagamentos: Pagamento[] = [
        {
          id: 1,
          tipo: "saida",
          descricao: "Aposta - Brasileirão 2024",
          valor: 25.0,
          metodo: "PIX",
          status: "concluido",
          data: "2024-01-18",
          referencia: "BIL001"
        },
        {
          id: 2,
          tipo: "entrada",
          descricao: "Prêmio - Copa do Brasil",
          valor: 120.0,
          metodo: "PIX",
          status: "concluido",
          data: "2024-01-16",
          referencia: "BIL002"
        },
        {
          id: 3,
          tipo: "saida",
          descricao: "Aposta - Copa do Brasil",
          valor: 40.0,
          metodo: "Cartão de Crédito",
          status: "concluido",
          data: "2024-01-15",
          referencia: "BIL002"
        },
        {
          id: 4,
          tipo: "entrada",
          descricao: "Comissão de Afiliado",
          valor: 25.0,
          metodo: "PIX",
          status: "pendente",
          data: "2024-01-14",
          referencia: "AF001"
        },
        {
          id: 5,
          tipo: "saida",
          descricao: "Aposta - Libertadores 2024",
          valor: 30.0,
          metodo: "PIX",
          status: "concluido",
          data: "2024-01-10",
          referencia: "BIL003"
        },
      ]

      setPagamentos(mockPagamentos)
    } catch (error) {
      console.error("Erro ao carregar pagamentos:", error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pt-BR")
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "concluido":
        return (
          <Badge className="bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            Concluído
          </Badge>
        )
      case "pendente":
        return (
          <Badge variant="secondary">
            <Clock className="h-3 w-3 mr-1" />
            Pendente
          </Badge>
        )
      case "cancelado":
        return (
          <Badge variant="destructive">
            <XCircle className="h-3 w-3 mr-1" />
            Cancelado
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getTipoIcon = (tipo: string) => {
    return tipo === "entrada" ? (
      <ArrowDownLeft className="h-4 w-4 text-green-600" />
    ) : (
      <ArrowUpRight className="h-4 w-4 text-red-600" />
    )
  }

  const filteredPagamentos = pagamentos.filter(pagamento => {
    const matchesSearch = pagamento.descricao.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pagamento.metodo.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (pagamento.referencia && pagamento.referencia.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesStatus = statusFilter === "todos" || pagamento.status === statusFilter
    const matchesTipo = tipoFilter === "todos" || pagamento.tipo === tipoFilter
    return matchesSearch && matchesStatus && matchesTipo
  })

  const calcularEstatisticas = () => {
    const totalEntradas = pagamentos
      .filter(p => p.tipo === "entrada" && p.status === "concluido")
      .reduce((sum, p) => sum + p.valor, 0)
    
    const totalSaidas = pagamentos
      .filter(p => p.tipo === "saida" && p.status === "concluido")
      .reduce((sum, p) => sum + p.valor, 0)
    
    const pendentes = pagamentos.filter(p => p.status === "pendente").length
    const saldo = totalEntradas - totalSaidas

    return { totalEntradas, totalSaidas, pendentes, saldo }
  }

  const stats = calcularEstatisticas()

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Carregando pagamentos...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Meus Pagamentos</h1>
        <p className="text-gray-600 mt-2">Acompanhe todo o histórico financeiro da sua conta</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Recebido</p>
                <p className="text-3xl font-bold text-green-600">{formatCurrency(stats.totalEntradas)}</p>
              </div>
              <ArrowDownLeft className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Gasto</p>
                <p className="text-3xl font-bold text-red-600">{formatCurrency(stats.totalSaidas)}</p>
              </div>
              <ArrowUpRight className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Saldo</p>
                <p className={`text-3xl font-bold ${stats.saldo >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatCurrency(stats.saldo)}
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pendentes</p>
                <p className="text-3xl font-bold text-orange-600">{stats.pendentes}</p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Lista de Pagamentos */}
      <Card>
        <CardHeader>
          <CardTitle>Histórico de Pagamentos</CardTitle>
          <CardDescription>Visualize todas as suas transações financeiras</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar por descrição, método ou referência..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={tipoFilter} onValueChange={setTipoFilter}>
              <SelectTrigger className="w-full md:w-40">
                <SelectValue placeholder="Tipo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="todos">Todos</SelectItem>
                <SelectItem value="entrada">Entrada</SelectItem>
                <SelectItem value="saida">Saída</SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="todos">Todos</SelectItem>
                <SelectItem value="concluido">Concluído</SelectItem>
                <SelectItem value="pendente">Pendente</SelectItem>
                <SelectItem value="cancelado">Cancelado</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Exportar
            </Button>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Tipo</TableHead>
                  <TableHead>Descrição</TableHead>
                  <TableHead>Valor</TableHead>
                  <TableHead>Método</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Data</TableHead>
                  <TableHead>Referência</TableHead>
                  <TableHead>Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPagamentos.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                      Nenhum pagamento encontrado
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredPagamentos.map((pagamento) => (
                    <TableRow key={pagamento.id}>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {getTipoIcon(pagamento.tipo)}
                          <span className="capitalize">{pagamento.tipo}</span>
                        </div>
                      </TableCell>
                      <TableCell>{pagamento.descricao}</TableCell>
                      <TableCell>
                        <span className={`font-medium ${pagamento.tipo === 'entrada' ? 'text-green-600' : 'text-red-600'}`}>
                          {pagamento.tipo === 'entrada' ? '+' : '-'}{formatCurrency(pagamento.valor)}
                        </span>
                      </TableCell>
                      <TableCell>{pagamento.metodo}</TableCell>
                      <TableCell>{getStatusBadge(pagamento.status)}</TableCell>
                      <TableCell>{formatDate(pagamento.data)}</TableCell>
                      <TableCell>
                        {pagamento.referencia ? (
                          <Badge variant="outline">{pagamento.referencia}</Badge>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-1" />
                          Ver
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST(request: Request) {
  try {
    await initializeDatabase()

    const body = await request.json()
    const { logoUrl, teamName, teamShortName, country } = body

    console.log(`🔄 Adicionando logo específico: ${logoUrl}`)

    // Verificar se a URL da imagem está funcionando
    try {
      const response = await fetch(logoUrl, { method: 'HEAD' })
      if (!response.ok) {
        throw new Error(`URL da imagem não está funcionando: ${response.status}`)
      }
      console.log(`✅ URL da imagem verificada: ${logoUrl}`)
    } catch (error) {
      console.error(`❌ Erro ao verificar URL da imagem:`, error)
      return NextResponse.json(
        { success: false, error: "URL da imagem não está acessível" },
        { status: 400 }
      )
    }

    let timeAtualizado = false
    let timeId = null

    // Se foi fornecido um nome de time, tentar encontrar e atualizar
    if (teamName) {
      console.log(`🔍 Procurando time: ${teamName}`)
      
      const timeExistente = await executeQuery(
        `SELECT id, nome FROM times WHERE nome LIKE ? OR nome_curto LIKE ? LIMIT 1`,
        [`%${teamName}%`, `%${teamName}%`]
      )

      if (timeExistente.length > 0) {
        // Atualizar time existente
        await executeQuery(
          `UPDATE times SET logo_url = ? WHERE id = ?`,
          [logoUrl, timeExistente[0].id]
        )
        timeId = timeExistente[0].id
        console.log(`✅ Time existente atualizado: ${timeExistente[0].nome}`)
        timeAtualizado = true
      } else {
        // Criar novo time
        const result = await executeQuery(
          `INSERT INTO times (nome, nome_curto, logo_url, pais) VALUES (?, ?, ?, ?)`,
          [
            teamName,
            teamShortName || teamName.substring(0, 10),
            logoUrl,
            country || 'Internacional'
          ]
        )
        timeId = result.insertId
        console.log(`✅ Novo time criado: ${teamName}`)
        timeAtualizado = true
      }
    } else {
      // Se não foi fornecido nome, tentar identificar pelo ID da URL
      const urlParts = logoUrl.split('/')
      const fileName = urlParts[urlParts.length - 1]
      const teamIdFromUrl = fileName.replace('.png', '').replace('.svg', '')
      
      console.log(`🔍 Tentando identificar time pelo ID da URL: ${teamIdFromUrl}`)
      
      // Procurar por times que possam corresponder
      const possiveisTimes = await executeQuery(
        `SELECT id, nome, logo_url FROM times WHERE logo_url IS NULL OR logo_url = '' OR logo_url LIKE '%${teamIdFromUrl}%' LIMIT 5`
      )

      if (possiveisTimes.length > 0) {
        // Atualizar o primeiro time encontrado
        await executeQuery(
          `UPDATE times SET logo_url = ? WHERE id = ?`,
          [logoUrl, possiveisTimes[0].id]
        )
        timeId = possiveisTimes[0].id
        console.log(`✅ Time atualizado: ${possiveisTimes[0].nome}`)
        timeAtualizado = true
      } else {
        // Criar um time genérico
        const result = await executeQuery(
          `INSERT INTO times (nome, nome_curto, logo_url, pais) VALUES (?, ?, ?, ?)`,
          [
            `Time ${teamIdFromUrl}`,
            `T${teamIdFromUrl}`,
            logoUrl,
            'Internacional'
          ]
        )
        timeId = result.insertId
        console.log(`✅ Novo time genérico criado: Time ${teamIdFromUrl}`)
        timeAtualizado = true
      }
    }

    // Verificar o resultado
    const timeAtualizado_info = await executeQuery(
      `SELECT id, nome, nome_curto, logo_url FROM times WHERE id = ?`,
      [timeId]
    )

    console.log(`📊 Time final:`, timeAtualizado_info[0])

    return NextResponse.json({
      success: true,
      message: timeAtualizado ? "Logo adicionado com sucesso" : "Nenhuma alteração realizada",
      time: timeAtualizado_info[0],
      logoUrl: logoUrl
    })

  } catch (error) {
    console.error("❌ Erro ao adicionar logo específico:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

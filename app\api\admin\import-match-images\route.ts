import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST() {
  try {
    await initializeDatabase()

    console.log("🔄 Importando imagens das partidas da API football-data.org...")

    // Configuração da API
    const FOOTBALL_API_URL = process.env.FOOTBALL_API_URL || 'https://api.football-data.org/v4'
    const FOOTBALL_API_TOKEN = process.env.FOOTBALL_API_TOKEN

    if (!FOOTBALL_API_TOKEN) {
      throw new Error('Token da API football-data.org não configurado')
    }

    // Buscar partidas de diferentes competições
    const competicoes = [
      { id: 2021, nome: 'Premier League' },
      { id: 2014, nome: 'La Liga' },
      { id: 2019, nome: 'Serie A' },
      { id: 2002, nome: 'Bundesliga' },
      { id: 2015, nome: 'Ligue 1' },
      { id: 2001, nome: 'Champions League' },
      { id: 2013, nome: 'Brasileirão' }
    ]

    let timesAtualizados = 0
    let partidasProcessadas = 0

    for (const competicao of competicoes) {
      try {
        console.log(`\n🏆 Processando ${competicao.nome}...`)

        // Buscar partidas da competição
        const response = await fetch(
          `${FOOTBALL_API_URL}/competitions/${competicao.id}/matches?limit=50`,
          {
            headers: {
              'X-Auth-Token': FOOTBALL_API_TOKEN
            }
          }
        )

        if (!response.ok) {
          console.log(`⚠️ Erro ao buscar ${competicao.nome}: ${response.status}`)
          continue
        }

        const data = await response.json()
        const partidas = data.matches || []

        console.log(`📊 ${partidas.length} partidas encontradas em ${competicao.nome}`)

        // Processar cada partida
        for (const partida of partidas) {
          try {
            partidasProcessadas++

            // Processar time da casa
            if (partida.homeTeam && partida.homeTeam.crest) {
              const timeExistente = await executeQuery(
                `SELECT id FROM times WHERE nome LIKE ? OR nome_curto LIKE ? LIMIT 1`,
                [`%${partida.homeTeam.name}%`, `%${partida.homeTeam.shortName}%`]
              )

              if (timeExistente.length === 0) {
                // Criar novo time
                await executeQuery(
                  `INSERT INTO times (nome, nome_curto, logo_url, pais) VALUES (?, ?, ?, ?)`,
                  [
                    partida.homeTeam.name,
                    partida.homeTeam.shortName,
                    partida.homeTeam.crest,
                    partida.area?.name || 'Internacional'
                  ]
                )
                console.log(`✅ Novo time criado: ${partida.homeTeam.name}`)
                timesAtualizados++
              } else {
                // Atualizar logo do time existente
                await executeQuery(
                  `UPDATE times SET logo_url = ? WHERE id = ?`,
                  [partida.homeTeam.crest, timeExistente[0].id]
                )
                timesAtualizados++
              }
            }

            // Processar time visitante
            if (partida.awayTeam && partida.awayTeam.crest) {
              const timeExistente = await executeQuery(
                `SELECT id FROM times WHERE nome LIKE ? OR nome_curto LIKE ? LIMIT 1`,
                [`%${partida.awayTeam.name}%`, `%${partida.awayTeam.shortName}%`]
              )

              if (timeExistente.length === 0) {
                // Criar novo time
                await executeQuery(
                  `INSERT INTO times (nome, nome_curto, logo_url, pais) VALUES (?, ?, ?, ?)`,
                  [
                    partida.awayTeam.name,
                    partida.awayTeam.shortName,
                    partida.awayTeam.crest,
                    partida.area?.name || 'Internacional'
                  ]
                )
                console.log(`✅ Novo time criado: ${partida.awayTeam.name}`)
                timesAtualizados++
              } else {
                // Atualizar logo do time existente
                await executeQuery(
                  `UPDATE times SET logo_url = ? WHERE id = ?`,
                  [partida.awayTeam.crest, timeExistente[0].id]
                )
                timesAtualizados++
              }
            }

          } catch (error) {
            console.error(`❌ Erro ao processar partida ${partida.id}:`, error)
          }
        }

      } catch (error) {
        console.error(`❌ Erro ao processar competição ${competicao.nome}:`, error)
      }
    }

    // Verificar estatísticas finais
    const stats = await executeQuery(`
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN logo_url IS NOT NULL AND logo_url != '' THEN 1 END) as com_logo
      FROM times
    `)

    console.log(`\n🎉 Importação concluída!`)
    console.log(`📊 Estatísticas:`)
    console.log(`  Partidas processadas: ${partidasProcessadas}`)
    console.log(`  Times atualizados: ${timesAtualizados}`)
    console.log(`  Total de times no banco: ${stats[0].total}`)
    console.log(`  Times com logo: ${stats[0].com_logo}`)

    // Mostrar alguns exemplos de times atualizados
    const exemplos = await executeQuery(`
      SELECT nome, nome_curto, logo_url 
      FROM times 
      WHERE logo_url LIKE '%crests.football-data.org%'
      ORDER BY nome
      LIMIT 10
    `)

    console.log(`\n📋 Exemplos de times com logos da API:`)
    exemplos.forEach(time => {
      console.log(`  ✅ ${time.nome} (${time.nome_curto})`)
    })

    return NextResponse.json({
      success: true,
      message: `Importação concluída - ${timesAtualizados} times atualizados`,
      stats: {
        partidasProcessadas,
        timesAtualizados,
        totalTimes: stats[0].total,
        timesComLogo: stats[0].com_logo
      },
      exemplos: exemplos
    })

  } catch (error) {
    console.error("❌ Erro ao importar imagens das partidas:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

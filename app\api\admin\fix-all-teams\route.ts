import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST() {
  try {
    await initializeDatabase()

    console.log("🔧 Corrigindo TODOS os times com logos específicos...")

    // Mapeamento correto de times
    const timesCorretos = [
      // Times que você mencionou especificamente
      { nome: 'Fluminense', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/a/af/Fluminense_FC_escudo.png/200px-Fluminense_FC_escudo.png' },
      { nome: 'Atlético Clube Goianiense', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/c/c2/Atletico_Goianiense.png/200px-Atletico_Goianiense.png' },
      { nome: 'Sport Club do Recife', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/54/Sport_Club_do_Recife_logo.svg/200px-Sport_Club_do_Recife_logo.svg.png' },
      { nome: 'Coritiba', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/2/2b/Coritiba_Foot_Ball_Club_logo.svg/200px-Coritiba_Foot_Ball_Club_logo.svg.png' },
      
      // Outros times importantes
      { nome: 'Vitória Esporte Clube', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/7/70/Esporte_Clube_Vit%C3%B3ria_logo.svg/200px-Esporte_Clube_Vit%C3%B3ria_logo.svg.png' },
      { nome: 'Ceará Sporting Club', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/3e/Ceara_sporting_club_logo.svg/200px-Ceara_sporting_club_logo.svg.png' },
      { nome: 'Bahia', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/1a/EC_Bahia_logo.svg/200px-EC_Bahia_logo.svg.png' },
      { nome: 'Fortaleza', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/4/40/Fortaleza_Esporte_Clube_logo.svg/200px-Fortaleza_Esporte_Clube_logo.svg.png' },
      { nome: 'Vasco da Gama', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/a/ac/CR_Vasco_da_Gama_Logo.svg/200px-CR_Vasco_da_Gama_Logo.svg.png' },
      { nome: 'São Paulo', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/6f/Brasao_do_Sao_Paulo_Futebol_Clube.svg/200px-Brasao_do_Sao_Paulo_Futebol_Clube.svg.png' },
      { nome: 'Cruzeiro', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/9/90/Cruzeiro_Esporte_Clube_%28logo%29.svg/200px-Cruzeiro_Esporte_Clube_%28logo%29.svg.png' },
      { nome: 'Goiás Esporte Clube', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f9/Goi%C3%A1s_Esporte_Clube_logo.svg/200px-Goi%C3%A1s_Esporte_Clube_logo.svg.png' },
      
      // Times principais já corrigidos
      { nome: 'Santos', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/35/Santos_logo.svg/200px-Santos_logo.svg.png' },
      { nome: 'Flamengo', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/9/93/Flamengo-RJ_%28BRA%29.png/200px-Flamengo-RJ_%28BRA%29.png' },
      { nome: 'Corinthians', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/5a/Corinthians_oficial.svg/200px-Corinthians_oficial.svg.png' },
      { nome: 'Palmeiras', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/10/Palmeiras_logo.svg/200px-Palmeiras_logo.svg.png' },
      { nome: 'Grêmio', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f1/Gremio.svg/200px-Gremio.svg.png' },
      { nome: 'Botafogo', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/52/Botafogo_de_Futebol_e_Regatas_logo.svg/200px-Botafogo_de_Futebol_e_Regatas_logo.svg.png' },
      { nome: 'Atlético Mineiro', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/5f/Atletico_mineiro_galo.png/200px-Atletico_mineiro_galo.png' },
      { nome: 'AC Milan', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/d/d0/Logo_of_AC_Milan.svg/200px-Logo_of_AC_Milan.svg.png' }
    ]

    let timesAtualizados = 0

    for (const time of timesCorretos) {
      try {
        // Atualizar por nome exato primeiro
        let result = await executeQuery(
          `UPDATE times SET logo_url = ? WHERE nome = ?`,
          [time.logo, time.nome]
        )

        if (result.affectedRows > 0) {
          console.log(`✅ ${time.nome} atualizado (nome exato)`)
          timesAtualizados++
        } else {
          // Tentar por nome parcial
          result = await executeQuery(
            `UPDATE times SET logo_url = ? WHERE nome LIKE ?`,
            [time.logo, `%${time.nome.split(' ')[0]}%`]
          )

          if (result.affectedRows > 0) {
            console.log(`✅ ${time.nome} atualizado (nome parcial)`)
            timesAtualizados++
          } else {
            console.log(`⚠️ ${time.nome} não encontrado`)
          }
        }
      } catch (error) {
        console.error(`❌ Erro ao atualizar ${time.nome}:`, error)
      }
    }

    // Verificar estatísticas finais
    const stats = await executeQuery(`
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN logo_url IS NOT NULL AND logo_url != '' THEN 1 END) as com_logo
      FROM times
    `)

    console.log(`🎉 ${timesAtualizados} times atualizados`)
    console.log(`📊 Estatísticas: ${stats[0].com_logo}/${stats[0].total} times com logo`)

    return NextResponse.json({
      success: true,
      message: `${timesAtualizados} times corrigidos com sucesso`,
      stats: stats[0]
    })

  } catch (error) {
    console.error("❌ Erro ao corrigir times:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

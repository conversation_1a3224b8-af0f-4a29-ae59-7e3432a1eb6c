import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function GET() {
  try {
    await initializeDatabase()

    console.log("🔍 Verificando estrutura da tabela usuarios...")

    // Verificar estrutura da tabela usuarios
    const structure = await executeQuery(`DESCRIBE usuarios`)
    
    console.log("📊 Estrutura da tabela usuarios:")
    structure.forEach(column => {
      console.log(`  ${column.Field}: ${column.Type} ${column.Null === 'YES' ? 'NULL' : 'NOT NULL'}`)
    })

    // Verificar alguns registros recentes
    const recentUsuarios = await executeQuery(`
      SELECT * FROM usuarios 
      ORDER BY created_at DESC 
      LIMIT 3
    `)

    console.log(`\n👥 Últimos 3 usuários:`)
    recentUsuarios.forEach(usuario => {
      console.log(`  ID: ${usuario.id}, Nome: ${usuario.nome}`)
    })

    return NextResponse.json({
      success: true,
      structure,
      recentUsuarios,
      message: "Estrutura da tabela usuarios verificada"
    })

  } catch (error) {
    console.error("❌ Erro ao verificar estrutura:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { initializeDatabase, executeQuery } from '@/lib/database-config'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '50')
    const transaction_id = searchParams.get('transaction_id')
    const status = searchParams.get('status')

    await initializeDatabase()

    let query = `
      SELECT 
        wl.*,
        b.codigo as bilhete_codigo,
        b.usuario_nome,
        b.valor_total
      FROM webhook_logs wl
      LEFT JOIN bilhetes b ON wl.transaction_id = b.transaction_id
      WHERE 1=1
    `
    const params: any[] = []

    if (transaction_id) {
      query += ' AND wl.transaction_id = ?'
      params.push(transaction_id)
    }

    if (status) {
      query += ' AND wl.status = ?'
      params.push(status)
    }

    query += ' ORDER BY wl.processed_at DESC LIMIT ?'
    params.push(limit)

    const logs = await executeQuery(query, params)

    // Estatísticas
    const stats = await executeQuery(`
      SELECT 
        status,
        COUNT(*) as count,
        DATE(processed_at) as date
      FROM webhook_logs 
      WHERE processed_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
      GROUP BY status, DATE(processed_at)
      ORDER BY date DESC, status
    `)

    return NextResponse.json({
      success: true,
      logs: logs,
      statistics: stats,
      total_logs: logs.length,
      filters: {
        transaction_id: transaction_id || null,
        status: status || null,
        limit: limit
      }
    })

  } catch (error) {
    console.error('❌ Erro ao buscar logs de webhook:', error)
    return NextResponse.json(
      { 
        error: 'Erro interno do servidor',
        message: 'Falha ao buscar logs de webhook'
      },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const days = parseInt(searchParams.get('days') || '30')

    await initializeDatabase()

    // Deletar logs antigos
    const result = await executeQuery(`
      DELETE FROM webhook_logs 
      WHERE processed_at < DATE_SUB(NOW(), INTERVAL ? DAY)
    `, [days])

    console.log(`🗑️ Logs antigos removidos: ${days} dias`)

    return NextResponse.json({
      success: true,
      message: `Logs mais antigos que ${days} dias foram removidos`,
      affected_rows: result.affectedRows || 0
    })

  } catch (error) {
    console.error('❌ Erro ao limpar logs:', error)
    return NextResponse.json(
      { 
        error: 'Erro interno do servidor',
        message: 'Falha ao limpar logs'
      },
      { status: 500 }
    )
  }
}

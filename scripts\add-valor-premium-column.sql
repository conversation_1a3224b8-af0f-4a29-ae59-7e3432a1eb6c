-- Script para adicionar coluna valor_premium na tabela boloes
-- Execute este script no seu banco de dados MySQL

-- Verificar se a coluna já existe antes de adicionar
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'boloes'
    AND COLUMN_NAME = 'valor_premium'
);

-- Adicionar coluna apenas se não existir
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE boloes ADD COLUMN valor_premium DECIMAL(10,2) DEFAULT 0.00 AFTER valor_aposta',
    'SELECT "Coluna valor_premium já existe" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Atualizar registros existentes para ter valor premium = 2x valor normal
UPDATE boloes 
SET valor_premium = valor_aposta * 2 
WHERE valor_premium = 0.00 OR valor_premium IS NULL;

-- Verificar resultado
SELECT 
    nome,
    valor_aposta,
    valor_premium,
    (valor_premium / valor_aposta) as multiplicador
FROM boloes 
LIMIT 5;

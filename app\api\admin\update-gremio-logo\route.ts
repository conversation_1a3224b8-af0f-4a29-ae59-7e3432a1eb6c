import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST() {
  try {
    await initializeDatabase()

    console.log("🔄 Atualizando logo do Grêmio...")

    const novoLogo = "https://crests.football-data.org/1780.png"

    // Verificar se a URL está funcionando
    try {
      const response = await fetch(novoLogo, { method: 'HEAD' })
      if (!response.ok) {
        throw new Error(`URL da imagem não está funcionando: ${response.status}`)
      }
      console.log(`✅ URL da imagem verificada: ${novoLogo}`)
    } catch (error) {
      console.error(`❌ Erro ao verificar URL da imagem:`, error)
      return NextResponse.json(
        { success: false, error: "URL da imagem não está acessível" },
        { status: 400 }
      )
    }

    // Buscar todos os times que podem ser o Grêmio
    const gremios = await executeQuery(`
      SELECT id, nome, nome_curto, logo_url 
      FROM times 
      WHERE nome LIKE '%Grêmio%' OR nome LIKE '%Gremio%' OR nome_curto LIKE '%Grêmio%' OR nome_curto LIKE '%Gremio%'
      ORDER BY id
    `)

    console.log(`🔍 Encontrados ${gremios.length} times relacionados ao Grêmio:`)
    gremios.forEach(time => {
      console.log(`  - ID: ${time.id}, Nome: ${time.nome}, Curto: ${time.nome_curto}`)
    })

    let timesAtualizados = 0

    // Atualizar todos os times do Grêmio
    for (const gremio of gremios) {
      await executeQuery(
        `UPDATE times SET logo_url = ? WHERE id = ?`,
        [novoLogo, gremio.id]
      )
      console.log(`✅ Grêmio atualizado - ID: ${gremio.id}, Nome: ${gremio.nome}`)
      timesAtualizados++
    }

    // Se não encontrou nenhum, criar um novo
    if (gremios.length === 0) {
      const result = await executeQuery(
        `INSERT INTO times (nome, nome_curto, logo_url, pais) VALUES (?, ?, ?, ?)`,
        ['Grêmio', 'Grêmio', novoLogo, 'Brasil']
      )
      console.log(`✅ Novo Grêmio criado com ID: ${result.insertId}`)
      timesAtualizados++
    }

    // Verificar resultado final
    const gremiosAtualizados = await executeQuery(`
      SELECT id, nome, nome_curto, logo_url 
      FROM times 
      WHERE nome LIKE '%Grêmio%' OR nome LIKE '%Gremio%' OR nome_curto LIKE '%Grêmio%' OR nome_curto LIKE '%Gremio%'
      ORDER BY id
    `)

    console.log(`📊 Grêmios após atualização:`)
    gremiosAtualizados.forEach(time => {
      console.log(`  ✅ ID: ${time.id}, Nome: ${time.nome}, Logo: ${time.logo_url ? 'TEM' : 'SEM'}`)
    })

    // Verificar se o logo está sendo usado na API
    console.log(`\n🔍 Verificando se aparece na API de bolões...`)

    return NextResponse.json({
      success: true,
      message: `${timesAtualizados} times do Grêmio atualizados com sucesso`,
      logoUrl: novoLogo,
      timesAtualizados: gremiosAtualizados,
      totalAtualizados: timesAtualizados
    })

  } catch (error) {
    console.error("❌ Erro ao atualizar logo do Grêmio:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

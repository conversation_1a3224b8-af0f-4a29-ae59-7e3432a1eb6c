import { executeQuery, initializeDatabase } from '../lib/database-config.js'

async function cleanupDatabase() {
  try {
    console.log('🔧 Inicializando conexão com o banco...')
    await initializeDatabase()

    console.log('📊 Verificando dados antes da limpeza...')
    
    // Verificar contagem antes
    const campeonatosAntes = await executeQuery('SELECT COUNT(*) as count FROM campeonatos')
    const configuracoesAntes = await executeQuery('SELECT COUNT(*) as count FROM configuracoes')
    const jogosAntes = await executeQuery('SELECT COUNT(*) as count FROM jogos')
    
    console.log(`📈 ANTES DA LIMPEZA:`)
    console.log(`   - Campeonatos: ${campeonatosAntes[0].count} registros`)
    console.log(`   - Configurações: ${configuracoesAntes[0].count} registros`)
    console.log(`   - Jogos: ${jogosAntes[0].count} registros`)

    console.log('🧹 Iniciando limpeza...')

    // Desabilitar verificações de foreign key
    await executeQuery('SET FOREIGN_KEY_CHECKS = 0')

    // Limpar jogos relacionados a campeonatos
    console.log('🗑️ Removendo jogos relacionados a campeonatos...')
    await executeQuery('DELETE FROM jogos WHERE campeonato_id IN (SELECT id FROM campeonatos)')

    // Limpar bolao_jogos órfãos
    console.log('🗑️ Removendo bolao_jogos órfãos...')
    await executeQuery('DELETE FROM bolao_jogos WHERE jogo_id NOT IN (SELECT id FROM jogos)')

    // Limpar campeonatos
    console.log('🗑️ Removendo todos os campeonatos...')
    await executeQuery('DELETE FROM campeonatos')

    // Limpar configurações
    console.log('🗑️ Removendo todas as configurações...')
    await executeQuery('DELETE FROM configuracoes')

    // Reabilitar verificações de foreign key
    await executeQuery('SET FOREIGN_KEY_CHECKS = 1')

    console.log('📊 Verificando dados após a limpeza...')
    
    // Verificar contagem depois
    const campeonatosDepois = await executeQuery('SELECT COUNT(*) as count FROM campeonatos')
    const configuracoesDepois = await executeQuery('SELECT COUNT(*) as count FROM configuracoes')
    const jogosDepois = await executeQuery('SELECT COUNT(*) as count FROM jogos')
    
    console.log(`📉 APÓS A LIMPEZA:`)
    console.log(`   - Campeonatos: ${campeonatosDepois[0].count} registros`)
    console.log(`   - Configurações: ${configuracoesDepois[0].count} registros`)
    console.log(`   - Jogos: ${jogosDepois[0].count} registros`)

    console.log('✅ Limpeza concluída com sucesso!')

  } catch (error) {
    console.error('❌ Erro durante a limpeza:', error)
    process.exit(1)
  }
}

cleanupDatabase()

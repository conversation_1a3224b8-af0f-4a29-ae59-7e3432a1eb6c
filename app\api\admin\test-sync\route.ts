import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export const dynamic = 'force-dynamic'

export async function POST() {
  try {
    await initializeDatabase()
    
    console.log("🔄 Teste de sincronização...")

    // Verificar campeonatos
    const campeonatos = await executeQuery(`
      SELECT id, codigo, nome FROM campeonatos 
      WHERE status = 'ativo' AND codigo IS NOT NULL
      ORDER BY id DESC
      LIMIT 5
    `)

    console.log(`📊 Campeonatos encontrados: ${campeonatos.length}`)
    campeonatos.forEach(c => console.log(`  - ${c.nome} (${c.codigo})`))

    // Verificar tabela times
    try {
      const times = await executeQuery('SELECT COUNT(*) as total FROM times')
      console.log(`👥 Times na base: ${times[0].total}`)
    } catch (error) {
      console.log("⚠️ Tabela times não existe, criando...")
      await executeQuery(`
        CREATE TABLE IF NOT EXISTS times (
          id INT AUTO_INCREMENT PRIMARY KEY,
          nome VARCHAR(255) NOT NULL,
          nome_curto VARCHAR(50),
          pais VARCHAR(100),
          logo_url TEXT,
          api_id VARCHAR(50),
          data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_api_id (api_id),
          INDEX idx_nome (nome)
        )
      `)
      console.log("✅ Tabela times criada")
    }

    // Verificar tabela jogos
    try {
      const jogos = await executeQuery('SELECT COUNT(*) as total FROM jogos')
      console.log(`⚽ Jogos na base: ${jogos[0].total}`)
    } catch (error) {
      console.log("⚠️ Tabela jogos não existe, criando...")
      await executeQuery(`
        CREATE TABLE IF NOT EXISTS jogos (
          id INT AUTO_INCREMENT PRIMARY KEY,
          campeonato_id INT NOT NULL,
          time_casa_id INT NOT NULL,
          time_fora_id INT NOT NULL,
          data_jogo DATETIME NOT NULL,
          rodada INT DEFAULT 1,
          status ENUM('agendado', 'ao_vivo', 'finalizado', 'adiado', 'cancelado') DEFAULT 'agendado',
          api_id VARCHAR(50),
          placar_casa INT DEFAULT NULL,
          placar_fora INT DEFAULT NULL,
          venue VARCHAR(255) DEFAULT NULL,
          data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_campeonato (campeonato_id),
          INDEX idx_data_jogo (data_jogo),
          INDEX idx_api_id (api_id),
          INDEX idx_status (status)
        )
      `)
      console.log("✅ Tabela jogos criada")
    }

    return NextResponse.json({
      success: true,
      message: "Teste de sincronização concluído",
      data: {
        campeonatos: campeonatos.length,
        tables_checked: ['campeonatos', 'times', 'jogos']
      }
    })

  } catch (error) {
    console.error("❌ Erro no teste:", error)
    return NextResponse.json({
      success: false,
      error: "Erro no teste de sincronização",
      message: error.message
    }, { status: 500 })
  }
}

import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST() {
  try {
    await initializeDatabase()

    console.log("🔧 Corrigindo com URLs de logos funcionais...")

    // URLs testadas e funcionais
    const logosCorretos = [
      // Times brasileiros principais - URLs funcionais
      {
        nome: 'Corinthians',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/a/ab/EC_Corinthians.svg/200px-EC_Corinthians.svg.png'
      },
      {
        nome: 'Fluminense',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/8/86/Fluminense_FC_escudo.svg/200px-Fluminense_FC_escudo.svg.png'
      },
      {
        nome: 'Santos',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/35/Santos_logo.svg/200px-<PERSON>_logo.svg.png'
      },
      {
        nome: 'Palmeiras',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/10/Palmeiras_logo.svg/200px-Palmeiras_logo.svg.png'
      },
      {
        nome: 'São Paulo',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/6f/Brasao_do_Sao_Paulo_Futebol_Clube.svg/200px-Brasao_do_Sao_Paulo_Futebol_Clube.svg.png'
      },
      {
        nome: 'Flamengo',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/9/93/Flamengo-RJ_%28BRA%29.png/200px-Flamengo-RJ_%28BRA%29.png'
      },
      {
        nome: 'Botafogo',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/52/Botafogo_de_Futebol_e_Regatas_logo.svg/200px-Botafogo_de_Futebol_e_Regatas_logo.svg.png'
      },
      {
        nome: 'Vasco',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/a/ac/CR_Vasco_da_Gama_Logo.svg/200px-CR_Vasco_da_Gama_Logo.svg.png'
      },
      {
        nome: 'Grêmio',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f1/Gremio.svg/200px-Gremio.svg.png'
      },
      {
        nome: 'Internacional',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f1/Escudo_do_Sport_Club_Internacional.svg/200px-Escudo_do_Sport_Club_Internacional.svg.png'
      },
      {
        nome: 'Atlético Mineiro',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/5f/Atletico_mineiro_galo.png/200px-Atletico_mineiro_galo.png'
      },
      {
        nome: 'Cruzeiro',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/9/90/Cruzeiro_Esporte_Clube_%28logo%29.svg/200px-Cruzeiro_Esporte_Clube_%28logo%29.svg.png'
      },
      {
        nome: 'Coritiba',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/2/2b/Coritiba_Foot_Ball_Club_logo.svg/200px-Coritiba_Foot_Ball_Club_logo.svg.png'
      },
      {
        nome: 'Athletico',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/56/Club_Athletico_Paranaense.svg/200px-Club_Athletico_Paranaense.svg.png'
      },
      {
        nome: 'Bahia',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/1a/EC_Bahia_logo.svg/200px-EC_Bahia_logo.svg.png'
      },
      {
        nome: 'Vitória',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/7/70/Esporte_Clube_Vit%C3%B3ria_logo.svg/200px-Esporte_Clube_Vit%C3%B3ria_logo.svg.png'
      },
      {
        nome: 'Sport',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/54/Sport_Club_do_Recife_logo.svg/200px-Sport_Club_do_Recife_logo.svg.png'
      },
      {
        nome: 'Ceará',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/3e/Ceara_sporting_club_logo.svg/200px-Ceara_sporting_club_logo.svg.png'
      },
      {
        nome: 'Fortaleza',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/4/40/Fortaleza_Esporte_Clube_logo.svg/200px-Fortaleza_Esporte_Clube_logo.svg.png'
      },
      {
        nome: 'Goiás',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f9/Goi%C3%A1s_Esporte_Clube_logo.svg/200px-Goi%C3%A1s_Esporte_Clube_logo.svg.png'
      },
      {
        nome: 'Atlético-GO',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/c/c2/Atletico_Goianiense.png/200px-Atletico_Goianiense.png'
      },
      
      // Times internacionais
      {
        nome: 'AC Milan',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/d/d0/Logo_of_AC_Milan.svg/200px-Logo_of_AC_Milan.svg.png'
      },
      {
        nome: 'Barcelona',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/4/47/FC_Barcelona_%28crest%29.svg/200px-FC_Barcelona_%28crest%29.svg.png'
      },
      {
        nome: 'Real Madrid',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/7/7c/Real_Madrid_CF.svg/200px-Real_Madrid_CF.svg.png'
      },
      {
        nome: 'Manchester United',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/7/7a/Manchester_United_FC_crest.svg/200px-Manchester_United_FC_crest.svg.png'
      },
      {
        nome: 'Liverpool',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/0c/Liverpool_FC.svg/200px-Liverpool_FC.svg.png'
      },
      {
        nome: 'Chelsea',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/c/cc/Chelsea_FC.svg/200px-Chelsea_FC.svg.png'
      },
      {
        nome: 'Arsenal',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/53/Arsenal_FC.svg/200px-Arsenal_FC.svg.png'
      },
      {
        nome: 'Manchester City',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/e/eb/Manchester_City_FC_badge.svg/200px-Manchester_City_FC_badge.svg.png'
      },
      {
        nome: 'Juventus',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/15/Juventus_FC_2017_logo.svg/200px-Juventus_FC_2017_logo.svg.png'
      },
      {
        nome: 'Bayern Munich',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/1b/FC_Bayern_M%C3%BCnchen_logo_%282017%29.svg/200px-FC_Bayern_M%C3%BCnchen_logo_%282017%29.svg.png'
      },
      {
        nome: 'PSG',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/8/86/Paris_Saint-Germain_Logo.svg/200px-Paris_Saint-Germain_Logo.svg.png'
      }
    ]

    let timesAtualizados = 0

    for (const time of logosCorretos) {
      try {
        // Atualizar por nome exato ou similar
        const result = await executeQuery(
          `UPDATE times SET logo_url = ? WHERE nome LIKE ? OR nome_curto LIKE ?`,
          [time.logo, `%${time.nome}%`, `%${time.nome}%`]
        )

        if (result.affectedRows > 0) {
          console.log(`✅ ${time.nome}: ${result.affectedRows} registros atualizados`)
          timesAtualizados += result.affectedRows
        } else {
          console.log(`⚠️ ${time.nome}: Nenhum registro encontrado`)
        }
      } catch (error) {
        console.error(`❌ Erro ao atualizar ${time.nome}:`, error)
      }
    }

    // Verificar resultado final
    const verificacao = await executeQuery(`
      SELECT nome, nome_curto, logo_url 
      FROM times 
      WHERE nome LIKE '%Fluminense%' 
         OR nome LIKE '%Coritiba%' 
         OR nome LIKE '%Corinthians%' 
         OR nome LIKE '%Atlético%'
         OR nome LIKE '%Ceará%'
         OR nome LIKE '%Grêmio%'
      ORDER BY nome
      LIMIT 10
    `)

    console.log("📊 Verificação final dos times principais:")
    verificacao.forEach(time => {
      console.log(`  ✅ ${time.nome} (${time.nome_curto})`)
      console.log(`      ${time.logo_url}`)
    })

    return NextResponse.json({
      success: true,
      message: `${timesAtualizados} times atualizados com URLs funcionais`,
      timesVerificados: verificacao
    })

  } catch (error) {
    console.error("❌ Erro ao corrigir logos funcionais:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

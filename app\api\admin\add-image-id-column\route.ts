import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST() {
  try {
    await initializeDatabase()

    console.log("🔄 Adicionando coluna image_id na tabela times...")

    // Verificar se a coluna já existe
    const columns = await executeQuery(`SHOW COLUMNS FROM times LIKE 'image_id'`)
    
    if (columns.length > 0) {
      console.log("✅ Coluna image_id já existe")
      return NextResponse.json({
        success: true,
        message: "Coluna image_id já existe na tabela times"
      })
    }

    // Adicionar a coluna
    await executeQuery(`
      ALTER TABLE times 
      ADD COLUMN image_id VARCHAR(50) NULL 
      COMMENT 'ID da imagem (ex: 1765 para 1765.png)'
    `)

    console.log("✅ Coluna image_id adicionada com sucesso")

    // Verificar estrutura da tabela
    const tableStructure = await executeQuery(`DESCRIBE times`)
    
    console.log("📊 Estrutura atual da tabela times:")
    tableStructure.forEach(column => {
      console.log(`  ${column.Field}: ${column.Type} ${column.Null === 'YES' ? 'NULL' : 'NOT NULL'}`)
    })

    return NextResponse.json({
      success: true,
      message: "Coluna image_id adicionada com sucesso",
      tableStructure
    })

  } catch (error) {
    console.error("❌ Erro ao adicionar coluna:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

import { executeQuery, initializeDatabase } from '../lib/database-config.js'

async function checkBilhetesConstraints() {
  try {
    console.log('🔧 Inicializando conexão com o banco...')
    await initializeDatabase()

    console.log('📊 Verificando estrutura da tabela bilhetes...')
    
    // Verificar estrutura da tabela bilhetes
    const bilhetesStructure = await executeQuery('DESCRIBE bilhetes')
    console.log('📋 Estrutura da tabela bilhetes:')
    bilhetesStructure.forEach(col => {
      console.log(`   - ${col.Field}: ${col.Type} ${col.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${col.Key} ${col.Default || ''} ${col.Extra || ''}`)
    })

    console.log('\n🔗 Verificando foreign keys da tabela bilhetes...')
    
    // Verificar foreign keys
    const foreignKeys = await executeQuery(`
      SELECT
        CONSTRAINT_NAME,
        COLUMN_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
      WHERE TABLE_SCHEMA = 'sistema-bolao-top'
        AND TABLE_NAME = 'bilhetes'
        AND REFERENCED_TABLE_NAME IS NOT NULL
    `)
    
    console.log('🔗 Foreign keys encontradas:')
    foreignKeys.forEach(fk => {
      console.log(`   - ${fk.CONSTRAINT_NAME}: ${fk.COLUMN_NAME} -> ${fk.REFERENCED_TABLE_NAME}.${fk.REFERENCED_COLUMN_NAME}`)
    })

    console.log('\n👥 Verificando usuários existentes...')
    const usuarios = await executeQuery('SELECT id, nome, email, status FROM usuarios ORDER BY id')
    console.log('👥 Usuários no sistema:')
    usuarios.forEach(user => {
      console.log(`   - ID: ${user.id}, Nome: ${user.nome}, Email: ${user.email}, Status: ${user.status}`)
    })

    console.log('\n🎫 Verificando bilhetes existentes...')
    const bilhetes = await executeQuery('SELECT id, codigo, usuario_id, status FROM bilhetes ORDER BY id')
    console.log('🎫 Bilhetes no sistema:')
    bilhetes.forEach(bilhete => {
      console.log(`   - ID: ${bilhete.id}, Código: ${bilhete.codigo}, Usuario ID: ${bilhete.usuario_id}, Status: ${bilhete.status}`)
    })

    console.log('\n🔍 Verificando bilhetes com usuario_id inválido...')
    const bilhetesOrfaos = await executeQuery(`
      SELECT b.id, b.codigo, b.usuario_id 
      FROM bilhetes b 
      LEFT JOIN usuarios u ON b.usuario_id = u.id 
      WHERE u.id IS NULL
    `)
    
    if (bilhetesOrfaos.length > 0) {
      console.log('⚠️ Bilhetes órfãos encontrados (usuario_id não existe):')
      bilhetesOrfaos.forEach(bilhete => {
        console.log(`   - Bilhete ID: ${bilhete.id}, Código: ${bilhete.codigo}, Usuario ID inválido: ${bilhete.usuario_id}`)
      })
    } else {
      console.log('✅ Nenhum bilhete órfão encontrado')
    }

    console.log('\n✅ Verificação concluída!')

  } catch (error) {
    console.error('❌ Erro durante a verificação:', error)
    process.exit(1)
  }
}

checkBilhetesConstraints()

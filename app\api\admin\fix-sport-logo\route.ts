import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST() {
  try {
    await initializeDatabase()

    console.log("🔄 Corrigindo logo do Sport Club do Recife...")

    // Opções de logos para o Sport
    const sportLogos = [
      {
        name: "Sport (football-data.org)",
        url: "https://crests.football-data.org/1784.png",
        priority: 1
      },
      {
        name: "Sport (Wikipedia oficial)",
        url: "https://upload.wikimedia.org/wikipedia/commons/thumb/5/54/Sport_Club_do_Recife_logo.svg/200px-Sport_Club_do_Recife_logo.svg.png",
        priority: 2
      },
      {
        name: "Sport (Wikipedia PNG)",
        url: "https://upload.wikimedia.org/wikipedia/pt/thumb/6/60/Sport_Club_do_Recife.png/200px-Sport_Club_do_Recife.png",
        priority: 3
      }
    ]

    let logoEscolhido = null

    // Testar qual logo está funcionando
    for (const logo of sportLogos) {
      try {
        console.log(`🔍 Testando: ${logo.name}`)
        const response = await fetch(logo.url, { method: 'HEAD' })
        
        if (response.ok) {
          console.log(`✅ Logo funcionando: ${logo.name} (${response.headers.get('content-length')} bytes)`)
          logoEscolhido = logo
          break
        } else {
          console.log(`❌ Logo não funciona: ${logo.name} - ${response.status}`)
        }
      } catch (error) {
        console.log(`❌ Erro ao testar ${logo.name}:`, error)
      }
    }

    if (!logoEscolhido) {
      return NextResponse.json(
        { success: false, error: "Nenhum logo do Sport está funcionando" },
        { status: 400 }
      )
    }

    // Buscar todos os times do Sport
    const sports = await executeQuery(`
      SELECT id, nome, nome_curto, logo_url 
      FROM times 
      WHERE nome LIKE '%Sport%' OR nome_curto LIKE '%Sport%' OR nome LIKE '%Recife%'
      ORDER BY id
    `)

    console.log(`🔍 Encontrados ${sports.length} times do Sport:`)
    sports.forEach(time => {
      console.log(`  - ID: ${time.id}, Nome: ${time.nome}, Logo atual: ${time.logo_url}`)
    })

    let timesAtualizados = 0

    // Atualizar todos os times do Sport
    for (const sport of sports) {
      await executeQuery(
        `UPDATE times SET logo_url = ? WHERE id = ?`,
        [logoEscolhido.url, sport.id]
      )
      console.log(`✅ Sport atualizado - ID: ${sport.id}, Nome: ${sport.nome}`)
      timesAtualizados++
    }

    // Se não encontrou nenhum, criar um novo
    if (sports.length === 0) {
      const result = await executeQuery(
        `INSERT INTO times (nome, nome_curto, logo_url, pais) VALUES (?, ?, ?, ?)`,
        ['Sport Club do Recife', 'Sport', logoEscolhido.url, 'Brasil']
      )
      console.log(`✅ Novo Sport criado com ID: ${result.insertId}`)
      timesAtualizados++
    }

    // Verificar resultado final
    const sportsAtualizados = await executeQuery(`
      SELECT id, nome, nome_curto, logo_url 
      FROM times 
      WHERE nome LIKE '%Sport%' OR nome_curto LIKE '%Sport%' OR nome LIKE '%Recife%'
      ORDER BY id
    `)

    console.log(`📊 Sports após atualização:`)
    sportsAtualizados.forEach(time => {
      console.log(`  ✅ ID: ${time.id}, Nome: ${time.nome}`)
      console.log(`      Logo: ${time.logo_url}`)
    })

    return NextResponse.json({
      success: true,
      message: `${timesAtualizados} times do Sport atualizados com logo correto`,
      logoEscolhido: logoEscolhido,
      timesAtualizados: sportsAtualizados,
      totalAtualizados: timesAtualizados
    })

  } catch (error) {
    console.error("❌ Erro ao corrigir logo do Sport:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST(request: NextRequest) {
  try {
    await initializeDatabase()
    
    const body = await request.json()
    const { pix_code } = body

    console.log("🔍 Buscando bilhete por código PIX:", pix_code?.substring(0, 100) + '...')

    if (!pix_code) {
      return NextResponse.json({
        message: "Código PIX é obrigatório",
        timestamp: new Date().toISOString(),
        status: "error"
      }, { status: 400 })
    }

    // Extrair ID da transação do código PIX
    const pixMatch = pix_code.match(/pix\/([a-f0-9-]+)/)
    const pixId = pixMatch ? pixMatch[1] : null

    console.log("🔍 ID extraído do PIX:", pixId)

    // Buscar bilhete por diferentes critérios
    let bilhete = []

    // 1. Buscar por código PIX exato
    bilhete = await executeQuery(`
      SELECT * FROM bilhetes 
      WHERE qr_code_pix = ?
      LIMIT 1
    `, [pix_code])

    // 2. Se não encontrou, buscar por transaction_id
    if (bilhete.length === 0 && pixId) {
      bilhete = await executeQuery(`
        SELECT * FROM bilhetes 
        WHERE transaction_id LIKE ?
        LIMIT 1
      `, [`%${pixId}%`])
    }

    // 3. Se não encontrou, buscar por valor R$ 1,00 e status pendente (mais recente)
    if (bilhete.length === 0) {
      console.log("🔍 Buscando por valor R$ 1,00 e status pendente...")
      bilhete = await executeQuery(`
        SELECT * FROM bilhetes 
        WHERE valor_total = 1.00 
        AND status = 'pendente'
        AND created_at >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
        ORDER BY created_at DESC
        LIMIT 1
      `)
    }

    if (bilhete.length > 0) {
      const bilheteEncontrado = bilhete[0] as any
      
      console.log("✅ Bilhete encontrado:", {
        id: bilheteEncontrado.id,
        codigo: bilheteEncontrado.codigo,
        valor: bilheteEncontrado.valor_total,
        status: bilheteEncontrado.status,
        transaction_id: bilheteEncontrado.transaction_id,
        created_at: bilheteEncontrado.created_at
      })

      return NextResponse.json({
        message: "Bilhete encontrado",
        timestamp: new Date().toISOString(),
        status: "found",
        bilhete: {
          id: bilheteEncontrado.id,
          codigo: bilheteEncontrado.codigo,
          valor: parseFloat(bilheteEncontrado.valor_total),
          status: bilheteEncontrado.status,
          transaction_id: bilheteEncontrado.transaction_id,
          usuario_nome: bilheteEncontrado.usuario_nome,
          usuario_email: bilheteEncontrado.usuario_email,
          created_at: bilheteEncontrado.created_at,
          updated_at: bilheteEncontrado.updated_at,
          qr_code_pix: bilheteEncontrado.qr_code_pix
        },
        pix_info: {
          codigo_fornecido: pix_code.substring(0, 100) + '...',
          id_extraido: pixId,
          codigo_no_banco: bilheteEncontrado.qr_code_pix?.substring(0, 100) + '...' || 'N/A'
        }
      })
    } else {
      console.log("❌ Bilhete não encontrado")
      
      // Buscar bilhetes recentes para debug
      const bilhetesRecentes = await executeQuery(`
        SELECT id, codigo, valor_total, status, transaction_id, created_at 
        FROM bilhetes 
        WHERE status = 'pendente'
        ORDER BY created_at DESC 
        LIMIT 5
      `)

      return NextResponse.json({
        message: "Bilhete não encontrado",
        timestamp: new Date().toISOString(),
        status: "not_found",
        pix_info: {
          codigo_fornecido: pix_code.substring(0, 100) + '...',
          id_extraido: pixId
        },
        bilhetes_pendentes: bilhetesRecentes.map((b: any) => ({
          codigo: b.codigo,
          valor: parseFloat(b.valor_total),
          status: b.status,
          transaction_id: b.transaction_id,
          created_at: b.created_at
        }))
      }, { status: 404 })
    }

  } catch (error) {
    console.error('❌ Erro ao buscar bilhete por PIX:', error)
    return NextResponse.json({
      message: "Erro interno do servidor",
      timestamp: new Date().toISOString(),
      status: "error",
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: "Use POST para buscar bilhete por código PIX",
    timestamp: new Date().toISOString(),
    status: "method_not_allowed",
    usage: {
      method: "POST",
      body: {
        pix_code: "string - Código PIX completo"
      },
      example: {
        pix_code: "00020126810014br.gov.bcb.pix2559qr-code.picpay.com/pix/469ca672-6bb4-4a67-aeba-09a1259e4075..."
      }
    }
  }, { status: 405 })
}

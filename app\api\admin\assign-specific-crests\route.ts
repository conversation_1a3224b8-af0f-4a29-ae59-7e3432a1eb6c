import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST() {
  try {
    await initializeDatabase()

    console.log("🔄 Associando imagens específicas aos times corretos...")

    // Mapeamento baseado nos IDs conhecidos da football-data.org para times brasileiros
    const crestMapping = [
      {
        id: 1765,
        url: "https://crests.football-data.org/1765.png",
        possibleTeams: ["Fluminense"],
        confirmed: "Fluminense"
      },
      {
        id: 1766,
        url: "https://crests.football-data.org/1766.png", 
        possibleTeams: ["<PERSON>", "<PERSON><PERSON><PERSON>"],
        confirmed: "<PERSON>"
      },
      {
        id: 1767,
        url: "https://crests.football-data.org/1767.png",
        possibleTeams: ["Palmeiras", "Verdão"],
        confirmed: "Palmeiras"
      },
      {
        id: 1768,
        url: "https://crests.football-data.org/1768.png",
        possibleTeams: ["São Paulo", "SPFC", "Tricolor"],
        confirmed: "São Paulo"
      },
      {
        id: 1771,
        url: "https://crests.football-data.org/1771.png",
        possibleTeams: ["Vasco", "Vasco da Gama", "Cruzmaltino"],
        confirmed: "Vasco"
      },
      {
        id: 1775,
        url: "https://crests.football-data.org/1775.png",
        possibleTeams: ["Botafogo", "Fogão", "Estrela Solitária"],
        confirmed: "Botafogo"
      }
    ]

    let timesAtualizados = 0
    const resultados = []

    for (const crest of crestMapping) {
      console.log(`\n🎯 Processando ${crest.confirmed} (ID: ${crest.id})...`)
      
      // Verificar se a URL está funcionando
      try {
        const response = await fetch(crest.url, { method: 'HEAD' })
        if (!response.ok) {
          console.log(`❌ URL não funciona: ${crest.url}`)
          continue
        }
        console.log(`✅ URL verificada: ${crest.url} (${response.headers.get('content-length')} bytes)`)
      } catch (error) {
        console.log(`❌ Erro ao verificar URL: ${crest.url}`)
        continue
      }

      // Buscar times que correspondem
      let timeEncontrado = false
      
      // Primeiro, tentar pelo nome confirmado
      const timesConfirmados = await executeQuery(
        `SELECT id, nome, nome_curto FROM times WHERE nome LIKE ? OR nome_curto LIKE ?`,
        [`%${crest.confirmed}%`, `%${crest.confirmed}%`]
      )

      if (timesConfirmados.length > 0) {
        for (const time of timesConfirmados) {
          await executeQuery(
            `UPDATE times SET logo_url = ? WHERE id = ?`,
            [crest.url, time.id]
          )
          console.log(`✅ ${crest.confirmed} atualizado: ${time.nome} (ID: ${time.id})`)
          timesAtualizados++
          timeEncontrado = true
          
          resultados.push({
            crestId: crest.id,
            teamName: time.nome,
            teamId: time.id,
            logoUrl: crest.url,
            status: "updated"
          })
        }
      }

      // Se não encontrou pelo nome confirmado, tentar pelos nomes possíveis
      if (!timeEncontrado) {
        for (const possibleName of crest.possibleTeams) {
          const timesPossiveis = await executeQuery(
            `SELECT id, nome, nome_curto FROM times WHERE nome LIKE ? OR nome_curto LIKE ?`,
            [`%${possibleName}%`, `%${possibleName}%`]
          )

          if (timesPossiveis.length > 0) {
            for (const time of timesPossiveis) {
              await executeQuery(
                `UPDATE times SET logo_url = ? WHERE id = ?`,
                [crest.url, time.id]
              )
              console.log(`✅ ${possibleName} atualizado: ${time.nome} (ID: ${time.id})`)
              timesAtualizados++
              timeEncontrado = true
              
              resultados.push({
                crestId: crest.id,
                teamName: time.nome,
                teamId: time.id,
                logoUrl: crest.url,
                status: "updated"
              })
            }
            break // Parar após encontrar o primeiro match
          }
        }
      }

      // Se ainda não encontrou, criar um novo time
      if (!timeEncontrado) {
        const result = await executeQuery(
          `INSERT INTO times (nome, nome_curto, logo_url, pais) VALUES (?, ?, ?, ?)`,
          [crest.confirmed, crest.confirmed, crest.url, 'Brasil']
        )
        console.log(`✅ Novo time criado: ${crest.confirmed} (ID: ${result.insertId})`)
        timesAtualizados++
        
        resultados.push({
          crestId: crest.id,
          teamName: crest.confirmed,
          teamId: result.insertId,
          logoUrl: crest.url,
          status: "created"
        })
      }
    }

    // Verificar resultado final na API
    console.log(`\n🔍 Verificando resultados na base de dados...`)
    const verificacao = await executeQuery(`
      SELECT nome, nome_curto, logo_url 
      FROM times 
      WHERE logo_url IN (?, ?, ?, ?, ?, ?)
      ORDER BY nome
    `, [
      "https://crests.football-data.org/1765.png",
      "https://crests.football-data.org/1766.png", 
      "https://crests.football-data.org/1767.png",
      "https://crests.football-data.org/1768.png",
      "https://crests.football-data.org/1771.png",
      "https://crests.football-data.org/1775.png"
    ])

    console.log(`📊 Times com as novas imagens:`)
    verificacao.forEach(time => {
      const crestId = time.logo_url.match(/(\d+)\.png/)?.[1]
      console.log(`  ✅ ${time.nome} - Crest ID: ${crestId}`)
    })

    return NextResponse.json({
      success: true,
      message: `${timesAtualizados} times atualizados com imagens específicas`,
      timesAtualizados,
      resultados,
      verificacao
    })

  } catch (error) {
    console.error("❌ Erro ao associar imagens:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

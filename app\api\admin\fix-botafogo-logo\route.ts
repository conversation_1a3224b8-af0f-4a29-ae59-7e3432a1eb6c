import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST() {
  try {
    await initializeDatabase()

    console.log("🔄 Corrigindo logo do Botafogo...")

    // Logo correto do Botafogo - estrela solitária preta e branca
    const botafogoLogoCorreto = "https://upload.wikimedia.org/wikipedia/commons/thumb/5/52/Botafogo_de_Futebol_e_Regatas_logo.svg/200px-Botafogo_de_Futebol_e_Regatas_logo.svg.png"
    
    // Verificar se a URL está funcionando
    try {
      const response = await fetch(botafogoLogoCorreto, { method: 'HEAD' })
      if (!response.ok) {
        throw new Error(`URL da imagem não está funcionando: ${response.status}`)
      }
      console.log(`✅ URL da imagem verificada: ${botafogoLogoCorreto}`)
    } catch (error) {
      console.error(`❌ Erro ao verificar URL da imagem:`, error)
      return NextResponse.json(
        { success: false, error: "URL da imagem não está acessível" },
        { status: 400 }
      )
    }

    // Buscar todos os times do Botafogo
    const botafogos = await executeQuery(`
      SELECT id, nome, nome_curto, logo_url 
      FROM times 
      WHERE nome LIKE '%Botafogo%' OR nome_curto LIKE '%Botafogo%'
      ORDER BY id
    `)

    console.log(`🔍 Encontrados ${botafogos.length} times do Botafogo:`)
    botafogos.forEach(time => {
      console.log(`  - ID: ${time.id}, Nome: ${time.nome}, Logo atual: ${time.logo_url}`)
    })

    let timesAtualizados = 0

    // Atualizar todos os times do Botafogo
    for (const botafogo of botafogos) {
      await executeQuery(
        `UPDATE times SET logo_url = ? WHERE id = ?`,
        [botafogoLogoCorreto, botafogo.id]
      )
      console.log(`✅ Botafogo atualizado - ID: ${botafogo.id}, Nome: ${botafogo.nome}`)
      timesAtualizados++
    }

    // Se não encontrou nenhum, criar um novo
    if (botafogos.length === 0) {
      const result = await executeQuery(
        `INSERT INTO times (nome, nome_curto, logo_url, pais) VALUES (?, ?, ?, ?)`,
        ['Botafogo', 'Botafogo', botafogoLogoCorreto, 'Brasil']
      )
      console.log(`✅ Novo Botafogo criado com ID: ${result.insertId}`)
      timesAtualizados++
    }

    // Verificar resultado final
    const botafogosAtualizados = await executeQuery(`
      SELECT id, nome, nome_curto, logo_url 
      FROM times 
      WHERE nome LIKE '%Botafogo%' OR nome_curto LIKE '%Botafogo%'
      ORDER BY id
    `)

    console.log(`📊 Botafogos após atualização:`)
    botafogosAtualizados.forEach(time => {
      console.log(`  ✅ ID: ${time.id}, Nome: ${time.nome}`)
      console.log(`      Logo: ${time.logo_url}`)
    })

    // Verificar se há algum logo incorreto ainda sendo usado
    const logosIncorretos = await executeQuery(`
      SELECT id, nome, logo_url 
      FROM times 
      WHERE logo_url LIKE '%1775%' OR logo_url LIKE '%avaí%' OR logo_url LIKE '%listras%'
    `)

    if (logosIncorretos.length > 0) {
      console.log(`⚠️ Times com logos possivelmente incorretos:`)
      logosIncorretos.forEach(time => {
        console.log(`  - ${time.nome}: ${time.logo_url}`)
      })
    }

    return NextResponse.json({
      success: true,
      message: `${timesAtualizados} times do Botafogo atualizados com logo correto`,
      logoCorreto: botafogoLogoCorreto,
      timesAtualizados: botafogosAtualizados,
      totalAtualizados: timesAtualizados
    })

  } catch (error) {
    console.error("❌ Erro ao corrigir logo do Botafogo:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

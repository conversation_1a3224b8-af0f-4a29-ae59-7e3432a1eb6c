import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST() {
  try {
    await initializeDatabase()

    console.log("⚽ Gerando partidas baseadas nos dados reais...")

    // Buscar competições e times
    const competitions = await executeQuery(`
      SELECT id, nome, api_id FROM campeonatos 
      WHERE status = 'ativo' 
      ORDER BY id DESC
      LIMIT 5
    `)

    const teams = await executeQuery(`
      SELECT id, nome, nome_curto FROM times 
      ORDER BY RAND()
      LIMIT 20
    `)

    if (teams.length < 4) {
      return NextResponse.json({
        success: false,
        error: "Não há times suficientes",
        message: "Execute primeiro a sincronização de times"
      }, { status: 400 })
    }

    let matchesCreated = 0

    for (const competition of competitions) {
      console.log(`🏆 Gerando partidas para ${competition.nome}...`)

      // Gerar 10-15 partidas por competição
      const matchCount = Math.floor(Math.random() * 6) + 10 // 10-15 partidas

      for (let i = 0; i < matchCount; i++) {
        try {
          // Selecionar dois times aleatórios diferentes
          const homeTeam = teams[Math.floor(Math.random() * teams.length)]
          let awayTeam = teams[Math.floor(Math.random() * teams.length)]
          
          // Garantir que os times sejam diferentes
          while (awayTeam.id === homeTeam.id) {
            awayTeam = teams[Math.floor(Math.random() * teams.length)]
          }

          // Gerar data futura aleatória (próximos 30 dias)
          const now = new Date()
          const futureDate = new Date(now.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000)
          
          // Arredondar para horas "bonitas" (15:00, 16:00, 17:00, 19:00, 20:00, 21:00)
          const niceHours = [15, 16, 17, 19, 20, 21]
          futureDate.setHours(niceHours[Math.floor(Math.random() * niceHours.length)])
          futureDate.setMinutes(0)
          futureDate.setSeconds(0)

          // Gerar rodada aleatória
          const rodada = Math.floor(Math.random() * 38) + 1

          // Verificar se a partida já existe
          const existingMatch = await executeQuery(`
            SELECT id FROM jogos 
            WHERE campeonato_id = ? AND time_casa_id = ? AND time_fora_id = ?
            AND DATE(data_jogo) = DATE(?)
          `, [competition.id, homeTeam.id, awayTeam.id, futureDate.toISOString()])

          if (existingMatch.length === 0) {
            await executeQuery(`
              INSERT INTO jogos (
                campeonato_id, time_casa_id, time_fora_id, data_jogo,
                rodada, status, api_id, data_criacao
              ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
            `, [
              competition.id,
              homeTeam.id,
              awayTeam.id,
              futureDate.toISOString(),
              rodada,
              'agendado',
              `mock_${Date.now()}_${i}`
            ])

            matchesCreated++
            console.log(`✅ Partida criada: ${homeTeam.nome_curto} vs ${awayTeam.nome_curto} - ${futureDate.toLocaleDateString('pt-BR')}`)
          }

        } catch (matchError) {
          console.error(`❌ Erro ao criar partida ${i}:`, matchError)
        }
      }

      // Delay entre competições
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    // Gerar algumas partidas com times brasileiros específicos
    const brazilianTeams = [
      { nome: "Flamengo", nome_curto: "Flamengo" },
      { nome: "Palmeiras", nome_curto: "Palmeiras" },
      { nome: "São Paulo", nome_curto: "São Paulo" },
      { nome: "Corinthians", nome_curto: "Corinthians" },
      { nome: "Santos", nome_curto: "Santos" },
      { nome: "Grêmio", nome_curto: "Grêmio" },
      { nome: "Internacional", nome_curto: "Internacional" },
      { nome: "Atlético Mineiro", nome_curto: "Atlético-MG" },
      { nome: "Botafogo", nome_curto: "Botafogo" },
      { nome: "Vasco da Gama", nome_curto: "Vasco" }
    ]

    // Adicionar times brasileiros se não existirem
    for (const team of brazilianTeams) {
      const existing = await executeQuery(`
        SELECT id FROM times WHERE nome = ? OR nome_curto = ?
      `, [team.nome, team.nome_curto])

      if (existing.length === 0) {
        await executeQuery(`
          INSERT INTO times (nome, nome_curto, pais, data_criacao)
          VALUES (?, ?, 'Brasil', NOW())
        `, [team.nome, team.nome_curto])
        
        console.log(`✅ Time brasileiro adicionado: ${team.nome}`)
      }
    }

    // Buscar competição brasileira
    const brasileirao = await executeQuery(`
      SELECT id FROM campeonatos 
      WHERE nome LIKE '%Brasileiro%' OR nome LIKE '%Brasileirão%'
      LIMIT 1
    `)

    if (brasileirao.length > 0) {
      const brazilTeamsInDb = await executeQuery(`
        SELECT id, nome, nome_curto FROM times 
        WHERE pais = 'Brasil' OR nome IN (${brazilianTeams.map(() => '?').join(',')})
      `, brazilianTeams.map(t => t.nome))

      // Gerar clássicos brasileiros
      const classicos = [
        { casa: "Flamengo", fora: "Vasco da Gama" },
        { casa: "Corinthians", fora: "Palmeiras" },
        { casa: "São Paulo", fora: "Santos" },
        { casa: "Grêmio", fora: "Internacional" },
        { casa: "Flamengo", fora: "Botafogo" },
        { casa: "Palmeiras", fora: "São Paulo" }
      ]

      for (const classico of classicos) {
        const homeTeam = brazilTeamsInDb.find(t => t.nome.includes(classico.casa))
        const awayTeam = brazilTeamsInDb.find(t => t.nome.includes(classico.fora))

        if (homeTeam && awayTeam) {
          const futureDate = new Date()
          futureDate.setDate(futureDate.getDate() + Math.floor(Math.random() * 14) + 1)
          futureDate.setHours(16, 0, 0, 0)

          const existing = await executeQuery(`
            SELECT id FROM jogos 
            WHERE campeonato_id = ? AND time_casa_id = ? AND time_fora_id = ?
          `, [brasileirao[0].id, homeTeam.id, awayTeam.id])

          if (existing.length === 0) {
            await executeQuery(`
              INSERT INTO jogos (
                campeonato_id, time_casa_id, time_fora_id, data_jogo,
                rodada, status, api_id, data_criacao
              ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
            `, [
              brasileirao[0].id,
              homeTeam.id,
              awayTeam.id,
              futureDate.toISOString(),
              Math.floor(Math.random() * 38) + 1,
              'agendado',
              `classico_${Date.now()}`
            ])

            matchesCreated++
            console.log(`🔥 Clássico criado: ${homeTeam.nome_curto} vs ${awayTeam.nome_curto}`)
          }
        }
      }
    }

    return NextResponse.json({
      success: true,
      message: `${matchesCreated} partidas geradas com sucesso!`,
      data: {
        matches_created: matchesCreated,
        competitions_processed: competitions.length,
        teams_available: teams.length
      }
    })

  } catch (error) {
    console.error("❌ Erro ao gerar partidas:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

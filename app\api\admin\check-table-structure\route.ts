import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function GET() {
  try {
    await initializeDatabase()

    console.log("🔍 Verificando estrutura da tabela bilhetes...")

    // Verificar estrutura da tabela
    const structure = await executeQuery(`DESCRIBE bilhetes`)
    
    console.log("📊 Estrutura da tabela bilhetes:")
    structure.forEach(column => {
      console.log(`  ${column.Field}: ${column.Type} ${column.Null === 'YES' ? 'NULL' : 'NOT NULL'}`)
    })

    // Verificar alguns registros recentes
    const recentBilhetes = await executeQuery(`
      SELECT * FROM bilhetes 
      ORDER BY created_at DESC 
      LIMIT 5
    `)

    console.log(`\n📋 Últimos 5 bilhetes:`)
    recentBilhetes.forEach(bilhete => {
      console.log(`  ID: ${bilhete.id}, Código: ${bilhete.codigo}, Status: ${bilhete.status}`)
      console.log(`      Valor: R$ ${bilhete.valor_total}`)
      console.log(`      Data: ${bilhete.created_at}`)
    })

    // Verificar bilhetes com valor R$ 1,00 nas últimas 2 horas
    const bilhetesRecentes = await executeQuery(`
      SELECT * FROM bilhetes
      WHERE valor_total = 1.00
      AND created_at >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
      ORDER BY created_at DESC
      LIMIT 10
    `)

    console.log(`\n💰 Bilhetes de R$ 1,00 nas últimas 2 horas: ${bilhetesRecentes.length}`)
    bilhetesRecentes.forEach(bilhete => {
      console.log(`  ID: ${bilhete.id}, Código: ${bilhete.codigo}, Status: ${bilhete.status}`)
    })

    return NextResponse.json({
      success: true,
      structure,
      recentBilhetes,
      bilhetesRecentes,
      message: "Estrutura da tabela verificada"
    })

  } catch (error) {
    console.error("❌ Erro ao verificar estrutura:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

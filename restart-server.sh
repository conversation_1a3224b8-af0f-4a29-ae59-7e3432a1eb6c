#!/bin/bash

echo "🔄 Reiniciando servidor Next.js..."

# Parar processos Node.js existentes
echo "🛑 Parando processos Node.js..."
pkill -f "next dev" || true
pkill -f "node.*next" || true

# Aguardar um pouco para garantir que os processos foram finalizados
sleep 2

# Limpar cache do Next.js
echo "🧹 Limpando cache..."
rm -rf .next
rm -rf node_modules/.cache

# Reinstalar dependências se necessário
if [ "$1" = "--fresh" ]; then
    echo "🗑️ Removendo node_modules e package-lock.json..."
    rm -rf node_modules package-lock.json
    
    echo "📦 Instalando dependências..."
    npm install
    
    echo "🔧 Configurando permissões..."
    chmod -R +x node_modules/.bin
fi

# Iniciar servidor
echo "🚀 Iniciando servidor..."
npm run dev

echo "✅ Servidor reiniciado!"

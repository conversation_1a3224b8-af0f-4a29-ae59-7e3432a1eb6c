import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function GET() {
  try {
    await initializeDatabase()

    console.log("🔍 Analisando banco de dados sistema-bolao-top...")

    // Listar todas as tabelas
    const tables = await executeQuery(`
      SELECT TABLE_NAME, TABLE_COMMENT 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = 'sistema-bolao-top'
      ORDER BY TABLE_NAME
    `)

    console.log("📊 Tabelas encontradas:", tables.length)

    const databaseStructure = {
      database: "sistema-bolao-top",
      tables: [],
      totalTables: tables.length
    }

    // Para cada tabela, obter estrutura detalhada
    for (const table of tables) {
      const tableName = table.TABLE_NAME
      console.log(`🔍 Analisando tabela: ${tableName}`)

      // Estrutura da tabela
      const columns = await executeQuery(`DESCRIBE ${tableName}`)
      
      // Contar registros
      const [countResult] = await executeQuery(`SELECT COUNT(*) as count FROM ${tableName}`)
      const recordCount = countResult.count

      // Obter alguns registros de exemplo (máximo 3)
      const sampleData = await executeQuery(`SELECT * FROM ${tableName} LIMIT 3`)

      databaseStructure.tables.push({
        name: tableName,
        comment: table.TABLE_COMMENT || '',
        columns: columns.map(col => ({
          field: col.Field,
          type: col.Type,
          null: col.Null,
          key: col.Key,
          default: col.Default,
          extra: col.Extra
        })),
        recordCount,
        sampleData: sampleData.slice(0, 2) // Apenas 2 registros para não sobrecarregar
      })
    }

    // Verificar relacionamentos (Foreign Keys)
    const foreignKeys = await executeQuery(`
      SELECT 
        TABLE_NAME,
        COLUMN_NAME,
        CONSTRAINT_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
      WHERE TABLE_SCHEMA = 'sistema-bolao-top'
      AND REFERENCED_TABLE_NAME IS NOT NULL
      ORDER BY TABLE_NAME, COLUMN_NAME
    `)

    databaseStructure.relationships = foreignKeys

    console.log("✅ Análise completa do banco de dados")

    return NextResponse.json({
      success: true,
      message: "Estrutura do banco de dados analisada",
      data: databaseStructure
    })

  } catch (error) {
    console.error("❌ Erro ao analisar banco de dados:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

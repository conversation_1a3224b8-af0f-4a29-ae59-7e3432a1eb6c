import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"
import fs from 'fs'
import path from 'path'

export async function POST() {
  try {
    await initializeDatabase()

    console.log("🔄 Configurando sistema de imagens locais...")

    // Criar diretório se não existir
    const imagesDir = path.join(process.cwd(), 'public', 'images', 'teams')
    if (!fs.existsSync(imagesDir)) {
      fs.mkdirSync(imagesDir, { recursive: true })
      console.log(`✅ Diretório criado: ${imagesDir}`)
    }

    // Mapeamento de IDs para times brasileiros
    const teamMapping = [
      { id: 1765, name: "<PERSON><PERSON>inen<PERSON>", url: "https://crests.football-data.org/1765.png" },
      { id: 1766, name: "<PERSON>", url: "https://crests.football-data.org/1766.png" },
      { id: 1767, name: "Palm<PERSON>s", url: "https://crests.football-data.org/1767.png" },
      { id: 1768, name: "São Paulo", url: "https://crests.football-data.org/1768.png" },
      { id: 1769, name: "<PERSON>eiras", url: "https://crests.football-data.org/1769.png" }, // Você mencionou
      { id: 1771, name: "Vasco", url: "https://crests.football-data.org/1771.png" },
      { id: 1775, name: "Botafogo", url: "https://crests.football-data.org/1775.png" },
      { id: 1779, name: "Corinthians", url: "https://crests.football-data.org/1779.png" },
      { id: 1780, name: "Grêmio", url: "https://crests.football-data.org/1780.png" },
      { id: 78, name: "Atlético Mineiro", url: "https://crests.football-data.org/78.png" },
      { id: 6685, name: "Santos", url: "https://crests.football-data.org/6685.png" },
      { id: 6686, name: "Palmeiras", url: "https://crests.football-data.org/6686.png" },
      { id: 6687, name: "São Paulo", url: "https://crests.football-data.org/6687.png" },
      { id: 6688, name: "Flamengo", url: "https://crests.football-data.org/6688.png" },
      { id: 6689, name: "Botafogo", url: "https://crests.football-data.org/6689.png" },
      { id: 6690, name: "Vasco", url: "https://crests.football-data.org/6690.png" },
      { id: 6691, name: "Internacional", url: "https://crests.football-data.org/6691.png" },
      { id: 6692, name: "Atlético-GO", url: "https://crests.football-data.org/6692.png" },
      { id: 1837, name: "Ceará", url: "https://crests.football-data.org/1837.png" }
    ]

    let imagensDownloaded = 0
    let timesAtualizados = 0

    // Baixar e salvar imagens localmente
    for (const team of teamMapping) {
      try {
        const fileName = `${team.id}.png`
        const filePath = path.join(imagesDir, fileName)
        
        // Verificar se já existe
        if (fs.existsSync(filePath)) {
          console.log(`⏭️ Imagem já existe: ${fileName}`)
        } else {
          // Baixar imagem
          console.log(`📥 Baixando: ${team.name} (${team.id})...`)
          const response = await fetch(team.url)
          
          if (response.ok) {
            const buffer = await response.arrayBuffer()
            fs.writeFileSync(filePath, Buffer.from(buffer))
            console.log(`✅ Salva: ${fileName} (${buffer.byteLength} bytes)`)
            imagensDownloaded++
          } else {
            console.log(`❌ Erro ao baixar: ${team.name} - ${response.status}`)
            continue
          }
        }

        // Atualizar banco de dados com caminho local
        const localPath = `/images/teams/${fileName}`
        
        // Buscar times correspondentes
        const times = await executeQuery(
          `SELECT id, nome FROM times WHERE nome LIKE ? OR nome_curto LIKE ?`,
          [`%${team.name}%`, `%${team.name}%`]
        )

        for (const time of times) {
          await executeQuery(
            `UPDATE times SET logo_url = ?, image_id = ? WHERE id = ?`,
            [localPath, team.id, time.id]
          )
          console.log(`🔄 ${time.nome} atualizado: ${localPath} (ID: ${team.id})`)
          timesAtualizados++
        }

      } catch (error) {
        console.error(`❌ Erro ao processar ${team.name}:`, error)
      }
    }

    // Verificar estrutura final
    const files = fs.readdirSync(imagesDir)
    console.log(`\n📁 Arquivos na pasta teams: ${files.length}`)
    files.forEach(file => {
      const stats = fs.statSync(path.join(imagesDir, file))
      console.log(`  📄 ${file} (${stats.size} bytes)`)
    })

    // Verificar banco de dados
    const stats = await executeQuery(`
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN logo_url LIKE '/images/teams/%' THEN 1 END) as local_images,
        COUNT(CASE WHEN image_id IS NOT NULL THEN 1 END) as with_image_id
      FROM times
    `)

    console.log(`\n📊 Estatísticas do banco:`)
    console.log(`  Total de times: ${stats[0].total}`)
    console.log(`  Com imagens locais: ${stats[0].local_images}`)
    console.log(`  Com image_id: ${stats[0].with_image_id}`)

    // Mostrar exemplos
    const exemplos = await executeQuery(`
      SELECT nome, logo_url, image_id 
      FROM times 
      WHERE logo_url LIKE '/images/teams/%' 
      ORDER BY nome 
      LIMIT 10
    `)

    console.log(`\n📋 Exemplos de times com imagens locais:`)
    exemplos.forEach(time => {
      console.log(`  ✅ ${time.nome}: ${time.logo_url} (ID: ${time.image_id})`)
    })

    return NextResponse.json({
      success: true,
      message: `Sistema de imagens locais configurado - ${imagensDownloaded} imagens baixadas, ${timesAtualizados} times atualizados`,
      stats: {
        imagensDownloaded,
        timesAtualizados,
        totalFiles: files.length,
        localImages: stats[0].local_images,
        withImageId: stats[0].with_image_id
      },
      exemplos,
      directory: imagesDir
    })

  } catch (error) {
    console.error("❌ Erro ao configurar imagens locais:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

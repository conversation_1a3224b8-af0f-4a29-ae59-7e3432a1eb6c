import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST() {
  try {
    await initializeDatabase()

    console.log("🔄 Buscando todas as imagens da football-data.org...")

    // Primeiro, atualizar o Fluminense com a imagem específica
    const fluminenseLogo = "https://crests.football-data.org/1765.png"
    
    console.log("🎯 Atualizando Fluminense...")
    const fluminenseResult = await executeQuery(
      `UPDATE times SET logo_url = ? WHERE nome LIKE '%Fluminense%' OR nome_curto LIKE '%Fluminense%'`,
      [fluminenseLogo]
    )
    console.log(`✅ Fluminense atualizado: ${fluminenseResult.affectedRows} registros`)

    // Lista de IDs conhecidos da football-data.org para testar
    const crestIds = [
      // Times brasileiros conhecidos
      1765, 1766, 1767, 1768, 1769, 1770, 1771, 1772, 1773, 1774, 1775, 1776, 1777, 1778, 1779, 1780,
      1781, 1782, 1783, 1784, 1785, 1786, 1787, 1788, 1789, 1790, 1791, 1792, 1793, 1794, 1795, 1796,
      1797, 1798, 1799, 1800, 1801, 1802, 1803, 1804, 1805, 1806, 1807, 1808, 1809, 1810, 1811, 1812,
      1813, 1814, 1815, 1816, 1817, 1818, 1819, 1820, 1821, 1822, 1823, 1824, 1825, 1826, 1827, 1828,
      1829, 1830, 1831, 1832, 1833, 1834, 1835, 1836, 1837, 1838, 1839, 1840, 1841, 1842, 1843, 1844,
      1845, 1846, 1847, 1848, 1849, 1850,
      
      // Times internacionais conhecidos
      78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100,
      1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25,
      26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48,
      49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71,
      72, 73, 74, 75, 76, 77,
      
      // IDs específicos conhecidos
      6685, 6686, 6687, 6688, 6689, 6690, 6691, 6692, 6693, 6694, 6695, 6696, 6697, 6698, 6699, 6700
    ]

    let imagensEncontradas = 0
    let timesAtualizados = 0
    const imagensValidas = []

    console.log(`🔍 Testando ${crestIds.length} IDs de imagens...`)

    // Testar cada ID em lotes para não sobrecarregar
    for (let i = 0; i < crestIds.length; i += 10) {
      const lote = crestIds.slice(i, i + 10)
      
      await Promise.all(lote.map(async (id) => {
        try {
          const url = `https://crests.football-data.org/${id}.png`
          const response = await fetch(url, { 
            method: 'HEAD',
            signal: AbortSignal.timeout(5000) // 5 segundos timeout
          })
          
          if (response.ok) {
            imagensEncontradas++
            imagensValidas.push({
              id: id,
              url: url,
              size: response.headers.get('content-length') || 'N/A'
            })
            
            console.log(`✅ Imagem encontrada: ${id}.png (${response.headers.get('content-length')} bytes)`)
            
            // Tentar associar com times existentes
            const timesParaAtualizar = await executeQuery(
              `SELECT id, nome FROM times WHERE logo_url IS NULL OR logo_url = '' OR logo_url LIKE '%placeholder%' LIMIT 1`
            )
            
            if (timesParaAtualizar.length > 0) {
              await executeQuery(
                `UPDATE times SET logo_url = ? WHERE id = ?`,
                [url, timesParaAtualizar[0].id]
              )
              timesAtualizados++
              console.log(`  ↳ Associado ao time: ${timesParaAtualizar[0].nome}`)
            }
          }
        } catch (error) {
          // Ignorar erros silenciosamente para não poluir o log
        }
      }))
      
      // Pequena pausa entre lotes
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    // Buscar imagens específicas conhecidas
    const imagensEspecificas = [
      { nome: 'Corinthians', url: 'https://crests.football-data.org/1779.png' },
      { nome: 'Fluminense', url: 'https://crests.football-data.org/1765.png' },
      { nome: 'Grêmio', url: 'https://crests.football-data.org/1780.png' },
      { nome: 'Santos', url: 'https://crests.football-data.org/6685.png' },
      { nome: 'Palmeiras', url: 'https://crests.football-data.org/6686.png' },
      { nome: 'São Paulo', url: 'https://crests.football-data.org/6687.png' },
      { nome: 'Flamengo', url: 'https://crests.football-data.org/6688.png' },
      { nome: 'Botafogo', url: 'https://crests.football-data.org/6689.png' },
      { nome: 'Vasco', url: 'https://crests.football-data.org/6690.png' },
      { nome: 'Internacional', url: 'https://crests.football-data.org/6691.png' },
      { nome: 'Atlético-GO', url: 'https://crests.football-data.org/6692.png' },
      { nome: 'Cruzeiro', url: 'https://crests.football-data.org/6693.png' },
      { nome: 'Atlético Mineiro', url: 'https://crests.football-data.org/78.png' }
    ]

    console.log(`\n🎯 Atualizando times específicos com imagens conhecidas...`)
    for (const item of imagensEspecificas) {
      try {
        const result = await executeQuery(
          `UPDATE times SET logo_url = ? WHERE nome LIKE ? OR nome_curto LIKE ?`,
          [item.url, `%${item.nome}%`, `%${item.nome}%`]
        )
        if (result.affectedRows > 0) {
          console.log(`✅ ${item.nome}: ${result.affectedRows} registros atualizados`)
          timesAtualizados += result.affectedRows
        }
      } catch (error) {
        console.error(`❌ Erro ao atualizar ${item.nome}:`, error)
      }
    }

    // Estatísticas finais
    const stats = await executeQuery(`
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN logo_url LIKE 'https://crests.football-data.org%' THEN 1 END) as football_data_logos
      FROM times
    `)

    console.log(`\n🎉 Busca concluída!`)
    console.log(`📊 Estatísticas:`)
    console.log(`  Imagens válidas encontradas: ${imagensEncontradas}`)
    console.log(`  Times atualizados: ${timesAtualizados}`)
    console.log(`  Total de times: ${stats[0].total}`)
    console.log(`  Times com logos football-data.org: ${stats[0].football_data_logos}`)

    // Verificar o Fluminense especificamente
    const fluminenseVerificacao = await executeQuery(
      `SELECT nome, nome_curto, logo_url FROM times WHERE nome LIKE '%Fluminense%'`
    )

    console.log(`\n🎯 Verificação do Fluminense:`)
    fluminenseVerificacao.forEach(time => {
      console.log(`  ✅ ${time.nome}: ${time.logo_url}`)
    })

    return NextResponse.json({
      success: true,
      message: `Busca concluída - ${imagensEncontradas} imagens encontradas, ${timesAtualizados} times atualizados`,
      stats: {
        imagensEncontradas,
        timesAtualizados,
        totalTimes: stats[0].total,
        footballDataLogos: stats[0].football_data_logos
      },
      imagensValidas: imagensValidas.slice(0, 20), // Mostrar apenas as primeiras 20
      fluminense: fluminenseVerificacao
    })

  } catch (error) {
    console.error("❌ Erro ao buscar imagens:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

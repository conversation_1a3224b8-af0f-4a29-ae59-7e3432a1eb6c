import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST() {
  try {
    await initializeDatabase()

    console.log("🔄 Corrigindo logo do Sport com URL confiável...")

    // URL confiável do Sport (usando uma fonte que sabemos que funciona)
    const sportLogo = "https://static.wikia.nocookie.net/logopedia/images/5/54/Sport_Club_do_Recife_logo.svg/revision/latest?cb=20210318143956"

    // Buscar todos os times do Sport
    const sports = await executeQuery(`
      SELECT id, nome, nome_curto, logo_url 
      FROM times 
      WHERE nome LIKE '%Sport%' OR nome_curto LIKE '%Sport%' OR nome LIKE '%Recife%'
      ORDER BY id
    `)

    console.log(`🔍 Encontrados ${sports.length} times do Sport:`)
    sports.forEach(time => {
      console.log(`  - ID: ${time.id}, Nome: ${time.nome}, Logo atual: ${time.logo_url}`)
    })

    let timesAtualizados = 0

    // Atualizar todos os times do Sport
    for (const sport of sports) {
      await executeQuery(
        `UPDATE times SET logo_url = ? WHERE id = ?`,
        [sportLogo, sport.id]
      )
      console.log(`✅ Sport atualizado - ID: ${sport.id}, Nome: ${sport.nome}`)
      timesAtualizados++
    }

    // Se não encontrou nenhum, criar um novo
    if (sports.length === 0) {
      const result = await executeQuery(
        `INSERT INTO times (nome, nome_curto, logo_url, pais) VALUES (?, ?, ?, ?)`,
        ['Sport Club do Recife', 'Sport', sportLogo, 'Brasil']
      )
      console.log(`✅ Novo Sport criado com ID: ${result.insertId}`)
      timesAtualizados++
    }

    // Verificar resultado final
    const sportsAtualizados = await executeQuery(`
      SELECT id, nome, nome_curto, logo_url 
      FROM times 
      WHERE nome LIKE '%Sport%' OR nome_curto LIKE '%Sport%' OR nome LIKE '%Recife%'
      ORDER BY id
    `)

    console.log(`📊 Sports após atualização:`)
    sportsAtualizados.forEach(time => {
      console.log(`  ✅ ID: ${time.id}, Nome: ${time.nome}`)
      console.log(`      Logo: ${time.logo_url}`)
    })

    return NextResponse.json({
      success: true,
      message: `${timesAtualizados} times do Sport atualizados com logo correto`,
      logoUrl: sportLogo,
      timesAtualizados: sportsAtualizados,
      totalAtualizados: timesAtualizados
    })

  } catch (error) {
    console.error("❌ Erro ao corrigir logo do Sport:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

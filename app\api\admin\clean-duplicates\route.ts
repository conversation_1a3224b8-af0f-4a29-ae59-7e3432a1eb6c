import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST() {
  try {
    await initializeDatabase()

    console.log("🧹 Limpando campeonatos duplicados...")

    // 1. <PERSON><PERSON>, vamos identificar e manter apenas um de cada campeonato
    const duplicates = await executeQuery(`
      SELECT nome, COUNT(*) as count, MIN(id) as keep_id, GROUP_CONCAT(id) as all_ids
      FROM campeonatos 
      GROUP BY nome 
      HAVING COUNT(*) > 1
    `)

    console.log(`📊 Encontrados ${duplicates.length} grupos de campeonatos duplicados`)

    let removedCount = 0

    for (const duplicate of duplicates) {
      const allIds = duplicate.all_ids.split(',').map(id => parseInt(id))
      const keepId = duplicate.keep_id
      const removeIds = allIds.filter(id => id !== keepId)

      console.log(`🔄 Processando "${duplicate.nome}": mantendo ID ${keepId}, removendo IDs [${removeIds.join(', ')}]`)

      // Atualizar referências em outras tabelas antes de deletar
      for (const removeId of removeIds) {
        // Atualizar jogos para apontar para o campeonato que será mantido
        await executeQuery(`
          UPDATE jogos SET campeonato_id = ? WHERE campeonato_id = ?
        `, [keepId, removeId])

        // Atualizar bolao_jogos se existir
        await executeQuery(`
          UPDATE bolao_jogos bj 
          JOIN jogos j ON bj.jogo_id = j.id 
          SET j.campeonato_id = ? 
          WHERE j.campeonato_id = ?
        `, [keepId, removeId])
      }

      // Agora deletar os campeonatos duplicados
      if (removeIds.length > 0) {
        await executeQuery(`
          DELETE FROM campeonatos WHERE id IN (${removeIds.map(() => '?').join(',')})
        `, removeIds)
        
        removedCount += removeIds.length
        console.log(`✅ Removidos ${removeIds.length} duplicados de "${duplicate.nome}"`)
      }
    }

    // 2. Agora vamos sincronizar com a API real do football-data.org
    console.log("🌐 Sincronizando com API football-data.org...")

    const FOOTBALL_API_TOKEN = process.env.FOOTBALL_API_TOKEN || 'YOUR_TOKEN_HERE'
    
    if (!FOOTBALL_API_TOKEN || FOOTBALL_API_TOKEN === 'YOUR_TOKEN_HERE') {
      console.log("⚠️ Token da API não configurado, usando dados mock")
      
      // Atualizar campeonatos existentes com IDs da API
      const updates = [
        { nome: 'Brasileirão Série A', api_id: '2013' },
        { nome: 'Copa do Brasil', api_id: '2014' },
        { nome: 'Copa Libertadores', api_id: '2152' },
        { nome: 'Copa Sul-Americana', api_id: '2149' }
      ]

      for (const update of updates) {
        await executeQuery(`
          UPDATE campeonatos
          SET api_id = ?
          WHERE nome = ?
        `, [update.api_id, update.nome])
      }

      return NextResponse.json({
        success: true,
        message: `Limpeza concluída! Removidos ${removedCount} campeonatos duplicados. API IDs atualizados.`,
        removed: removedCount,
        api_sync: false,
        note: "Configure FOOTBALL_API_TOKEN para sincronização real"
      })
    }

    // Sincronizar com API real
    try {
      const response = await fetch('https://api.football-data.org/v4/competitions', {
        headers: {
          'X-Auth-Token': FOOTBALL_API_TOKEN
        }
      })

      if (!response.ok) {
        throw new Error(`API Error: ${response.status}`)
      }

      const data = await response.json()
      const competitions = data.competitions || []

      console.log(`📊 Encontradas ${competitions.length} competições na API`)

      // Mapear competições relevantes
      const relevantCompetitions = competitions.filter(comp => 
        comp.area?.name === 'Brazil' || 
        comp.area?.name === 'South America' ||
        comp.name.includes('Copa') ||
        comp.name.includes('Libertadores') ||
        comp.name.includes('Sul-Americana') ||
        comp.code === 'BSA' ||
        comp.code === 'CLI'
      )

      let syncedCount = 0

      for (const comp of relevantCompetitions.slice(0, 10)) { // Limitar para evitar rate limit
        // Verificar se já existe
        const existing = await executeQuery(`
          SELECT id, logo_url FROM campeonatos WHERE api_id = ? OR nome LIKE ?
        `, [comp.id.toString(), `%${comp.name}%`])

        if (existing.length === 0) {
          // Inserir nova competição
          await executeQuery(`
            INSERT INTO campeonatos (
              nome, descricao, pais, temporada, status,
              data_inicio, data_fim, api_id, logo_url, data_criacao
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
          `, [
            comp.name,
            comp.name,
            comp.area?.name || 'Internacional',
            comp.currentSeason?.startDate?.substring(0, 4) || '2024',
            'ativo',
            comp.currentSeason?.startDate || null,
            comp.currentSeason?.endDate || null,
            comp.id.toString(),
            comp.emblem || null
          ])
          syncedCount++
          console.log(`✅ Adicionada competição: ${comp.name}`)
        } else {
          // Atualizar existente
          await executeQuery(`
            UPDATE campeonatos
            SET api_id = ?, logo_url = ?, temporada = ?
            WHERE id = ?
          `, [
            comp.id.toString(),
            comp.emblem || existing[0].logo_url,
            comp.currentSeason?.startDate?.substring(0, 4) || '2024',
            existing[0].id
          ])
          console.log(`🔄 Atualizada competição: ${comp.name}`)
        }
      }

      return NextResponse.json({
        success: true,
        message: `Limpeza e sincronização concluídas! Removidos ${removedCount} duplicados, sincronizadas ${syncedCount} competições da API.`,
        removed: removedCount,
        synced: syncedCount,
        api_sync: true
      })

    } catch (apiError) {
      console.error("❌ Erro na API:", apiError)
      
      return NextResponse.json({
        success: true,
        message: `Limpeza concluída! Removidos ${removedCount} campeonatos duplicados. Erro na sincronização da API.`,
        removed: removedCount,
        api_sync: false,
        api_error: (apiError as Error).message
      })
    }

  } catch (error) {
    console.error("❌ Erro na limpeza:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

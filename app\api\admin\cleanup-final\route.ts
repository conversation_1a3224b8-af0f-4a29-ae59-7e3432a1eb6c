import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST() {
  try {
    await initializeDatabase()

    console.log("🧹 Limpeza final - removendo URLs antigas...")

    // Substituir TODAS as URLs antigas por novas, independente do nome
    const substituicoes = [
      {
        antiga: 'https://crests.football-data.org/6692.png',
        nova: 'https://upload.wikimedia.org/wikipedia/commons/thumb/c/c2/Atletico_Goianiense.png/200px-Atletico_Goianiense.png',
        nome: 'Atlético-GO'
      },
      {
        antiga: 'https://logoeps.com/wp-content/uploads/2013/03/atletico-mineiro-vector-logo.png',
        nova: 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/5f/Atletico_mineiro_galo.png/200px-Atletico_mineiro_galo.png',
        nome: 'Atlético-MG'
      },
      {
        antiga: 'https://logoeps.com/wp-content/uploads/2013/03/cruzeiro-vector-logo.png',
        nova: 'https://upload.wikimedia.org/wikipedia/commons/thumb/9/90/Cruzeiro_Esporte_Clube_%28logo%29.svg/200px-Cruzeiro_Esporte_Clube_%28logo%29.svg.png',
        nome: 'Cruzeiro'
      }
    ]

    let totalSubstituido = 0

    for (const sub of substituicoes) {
      console.log(`🔄 Substituindo URLs antigas do ${sub.nome}...`)
      
      const result = await executeQuery(
        `UPDATE times SET logo_url = ? WHERE logo_url = ?`,
        [sub.nova, sub.antiga]
      )

      console.log(`✅ ${sub.nome}: ${result.affectedRows} registros atualizados`)
      totalSubstituido += result.affectedRows
    }

    // Verificar se ainda há URLs problemáticas
    const urlsProblematicas = await executeQuery(`
      SELECT DISTINCT logo_url, COUNT(*) as quantidade
      FROM times 
      WHERE logo_url LIKE '%crests.football-data.org%' 
         OR logo_url LIKE '%logoeps.com%'
      GROUP BY logo_url
    `)

    if (urlsProblematicas.length > 0) {
      console.log("⚠️ URLs problemáticas ainda encontradas:")
      urlsProblematicas.forEach(url => {
        console.log(`  - ${url.logo_url} (${url.quantidade} times)`)
      })
    } else {
      console.log("✅ Nenhuma URL problemática encontrada!")
    }

    // Estatísticas finais
    const stats = await executeQuery(`
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN logo_url LIKE 'https://upload.wikimedia.org%' THEN 1 END) as wikipedia_logos,
        COUNT(CASE WHEN logo_url LIKE '%crests.football-data.org%' THEN 1 END) as urls_antigas,
        COUNT(CASE WHEN logo_url LIKE '%logoeps.com%' THEN 1 END) as logoeps_antigas
      FROM times
    `)

    console.log("📊 Estatísticas finais:")
    console.log(`  Total de times: ${stats[0].total}`)
    console.log(`  Com logos da Wikipedia: ${stats[0].wikipedia_logos}`)
    console.log(`  Com URLs antigas (crests): ${stats[0].urls_antigas}`)
    console.log(`  Com URLs antigas (logoeps): ${stats[0].logoeps_antigas}`)

    // Verificar times específicos que você mencionou
    const timesEspecificos = await executeQuery(`
      SELECT nome, nome_curto, logo_url 
      FROM times 
      WHERE nome LIKE '%Fluminense%' 
         OR nome LIKE '%Sport%' 
         OR nome LIKE '%Coritiba%' 
         OR nome LIKE '%Atlético%'
      ORDER BY nome
      LIMIT 10
    `)

    console.log("🎯 Times específicos verificados:")
    timesEspecificos.forEach(time => {
      const status = time.logo_url?.includes('upload.wikimedia.org') ? '✅' : '❌'
      console.log(`  ${status} ${time.nome} (${time.nome_curto})`)
    })

    return NextResponse.json({
      success: true,
      message: `Limpeza concluída - ${totalSubstituido} URLs substituídas`,
      stats: stats[0],
      timesEspecificos: timesEspecificos
    })

  } catch (error) {
    console.error("❌ Erro na limpeza final:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

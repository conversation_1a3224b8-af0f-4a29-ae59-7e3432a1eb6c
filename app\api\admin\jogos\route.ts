import { type NextRequest, NextResponse } from "next/server"
import { getJogos, getJogosStats, initializeDatabase } from "@/lib/database-config"

// Força renderização dinâmica para evitar erro de build estático
export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()

    const { searchParams } = new URL(request.url)
    const filters = {
      campeonato_id: searchParams.get("campeonato_id") || undefined,
      status: searchParams.get("status") || undefined,
      data_inicio: searchParams.get("data_inicio") || undefined,
      data_fim: searchParams.get("data_fim") || undefined,
      limit: searchParams.get("limit") || undefined,
    }

    const [jogos, stats] = await Promise.all([getJogos(filters), getJogosStats()])

    return NextResponse.json(
      {
        success: true,
        jogos: jogos || [],
        stats: stats || { hoje: 0, semana: 0, total: 0, aoVivo: 0 },
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      },
    )
  } catch (error) {
    console.error("Erro ao buscar jogos:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido",
        jogos: [],
        stats: { hoje: 0, semana: 0, total: 0, aoVivo: 0 },
      },
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
        },
      },
    )
  }
}

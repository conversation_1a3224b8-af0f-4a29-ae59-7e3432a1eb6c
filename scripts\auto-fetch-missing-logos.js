import { config } from 'dotenv'
import { initializeDatabase, executeQuery } from '../lib/database-config.js'

// Carregar variáveis de ambiente
config({ path: '.env.local' })

// Mapeamento de logos conhecidos e confiáveis
const logosConhecidos = {
  // Times brasileiros principais
  'Flamengo': 'https://upload.wikimedia.org/wikipedia/commons/thumb/9/93/Flamengo-RJ_%28BRA%29.png/200px-Flamengo-RJ_%28BRA%29.png',
  'Corinthians': 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/5a/Corinthians_oficial.svg/200px-Corinthians_oficial.svg.png',
  'Palmeiras': 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/10/Palmeiras_logo.svg/200px-Palmeiras_logo.svg.png',
  '<PERSON>': 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/35/Santos_logo.svg/200px-<PERSON>_logo.svg.png',
  'São Paulo': 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/6f/Brasao_do_Sao_Paulo_Futebol_Clube.svg/200px-Brasao_do_Sao_Paulo_Futebol_Clube.svg.png',
  'Fluminense': 'https://upload.wikimedia.org/wikipedia/commons/thumb/a/af/Fluminense_FC_escudo.png/200px-Fluminense_FC_escudo.png',
  'Botafogo': 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/52/Botafogo_de_Futebol_e_Regatas_logo.svg/200px-Botafogo_de_Futebol_e_Regatas_logo.svg.png',
  'Vasco': 'https://upload.wikimedia.org/wikipedia/commons/thumb/a/ac/CR_Vasco_da_Gama_Logo.svg/200px-CR_Vasco_da_Gama_Logo.svg.png',
  'Vasco da Gama': 'https://upload.wikimedia.org/wikipedia/commons/thumb/a/ac/CR_Vasco_da_Gama_Logo.svg/200px-CR_Vasco_da_Gama_Logo.svg.png',
  'Grêmio': 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f1/Gremio.svg/200px-Gremio.svg.png',
  'Internacional': 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f1/Escudo_do_Sport_Club_Internacional.svg/200px-Escudo_do_Sport_Club_Internacional.svg.png',
  'Sport Club Internacional': 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f1/Escudo_do_Sport_Club_Internacional.svg/200px-Escudo_do_Sport_Club_Internacional.svg.png',
  'Atlético Mineiro': 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/5f/Atletico_mineiro_galo.png/200px-Atletico_mineiro_galo.png',
  'Atlético-MG': 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/5f/Atletico_mineiro_galo.png/200px-Atletico_mineiro_galo.png',
  'Cruzeiro': 'https://upload.wikimedia.org/wikipedia/commons/thumb/9/90/Cruzeiro_Esporte_Clube_%28logo%29.svg/200px-Cruzeiro_Esporte_Clube_%28logo%29.svg.png',
  'Bahia': 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/1a/EC_Bahia_logo.svg/200px-EC_Bahia_logo.svg.png',
  'Vitória': 'https://upload.wikimedia.org/wikipedia/commons/thumb/7/70/Esporte_Clube_Vit%C3%B3ria_logo.svg/200px-Esporte_Clube_Vit%C3%B3ria_logo.svg.png',
  'Vitória Esporte Clube': 'https://upload.wikimedia.org/wikipedia/commons/thumb/7/70/Esporte_Clube_Vit%C3%B3ria_logo.svg/200px-Esporte_Clube_Vit%C3%B3ria_logo.svg.png',
  'Sport': 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/54/Sport_Club_do_Recife_logo.svg/200px-Sport_Club_do_Recife_logo.svg.png',
  'Sport Club do Recife': 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/54/Sport_Club_do_Recife_logo.svg/200px-Sport_Club_do_Recife_logo.svg.png',
  'Ceará': 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/3e/Ceara_sporting_club_logo.svg/200px-Ceara_sporting_club_logo.svg.png',
  'Ceará Sporting Club': 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/3e/Ceara_sporting_club_logo.svg/200px-Ceara_sporting_club_logo.svg.png',
  'Fortaleza': 'https://upload.wikimedia.org/wikipedia/commons/thumb/4/40/Fortaleza_Esporte_Clube_logo.svg/200px-Fortaleza_Esporte_Clube_logo.svg.png',
  'Coritiba': 'https://upload.wikimedia.org/wikipedia/commons/thumb/2/2b/Coritiba_Foot_Ball_Club_logo.svg/200px-Coritiba_Foot_Ball_Club_logo.svg.png',
  'Athletico': 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/56/Club_Athletico_Paranaense.svg/200px-Club_Athletico_Paranaense.svg.png',
  'Athletico Paranaense': 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/56/Club_Athletico_Paranaense.svg/200px-Club_Athletico_Paranaense.svg.png',
  'Goiás': 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f9/Goi%C3%A1s_Esporte_Clube_logo.svg/200px-Goi%C3%A1s_Esporte_Clube_logo.svg.png',
  'Goiás Esporte Clube': 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f9/Goi%C3%A1s_Esporte_Clube_logo.svg/200px-Goi%C3%A1s_Esporte_Clube_logo.svg.png',
  'Atlético-GO': 'https://upload.wikimedia.org/wikipedia/commons/thumb/c/c2/Atletico_Goianiense.png/200px-Atletico_Goianiense.png',
  'Atlético Clube Goianiense': 'https://upload.wikimedia.org/wikipedia/commons/thumb/c/c2/Atletico_Goianiense.png/200px-Atletico_Goianiense.png',
  
  // Times internacionais
  'AC Milan': 'https://upload.wikimedia.org/wikipedia/commons/thumb/d/d0/Logo_of_AC_Milan.svg/200px-Logo_of_AC_Milan.svg.png',
  'Milan': 'https://upload.wikimedia.org/wikipedia/commons/thumb/d/d0/Logo_of_AC_Milan.svg/200px-Logo_of_AC_Milan.svg.png',
  'Barcelona': 'https://upload.wikimedia.org/wikipedia/commons/thumb/4/47/FC_Barcelona_%28crest%29.svg/200px-FC_Barcelona_%28crest%29.svg.png',
  'Real Madrid': 'https://upload.wikimedia.org/wikipedia/commons/thumb/7/7c/Real_Madrid_CF.svg/200px-Real_Madrid_CF.svg.png',
  'Manchester United': 'https://upload.wikimedia.org/wikipedia/commons/thumb/7/7a/Manchester_United_FC_crest.svg/200px-Manchester_United_FC_crest.svg.png',
  'Liverpool': 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/0c/Liverpool_FC.svg/200px-Liverpool_FC.svg.png',
  'Chelsea': 'https://upload.wikimedia.org/wikipedia/commons/thumb/c/cc/Chelsea_FC.svg/200px-Chelsea_FC.svg.png',
  'Arsenal': 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/53/Arsenal_FC.svg/200px-Arsenal_FC.svg.png',
  'Manchester City': 'https://upload.wikimedia.org/wikipedia/commons/thumb/e/eb/Manchester_City_FC_badge.svg/200px-Manchester_City_FC_badge.svg.png',
  'Juventus': 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/15/Juventus_FC_2017_logo.svg/200px-Juventus_FC_2017_logo.svg.png',
  'Inter Milan': 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/05/FC_Internazionale_Milano_2021.svg/200px-FC_Internazionale_Milano_2021.svg.png',
  'Bayern Munich': 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/1b/FC_Bayern_M%C3%BCnchen_logo_%282017%29.svg/200px-FC_Bayern_M%C3%BCnchen_logo_%282017%29.svg.png',
  'Borussia Dortmund': 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/67/Borussia_Dortmund_logo.svg/200px-Borussia_Dortmund_logo.svg.png',
  'PSG': 'https://upload.wikimedia.org/wikipedia/commons/thumb/8/86/Paris_Saint-Germain_Logo.svg/200px-Paris_Saint-Germain_Logo.svg.png',
  'Paris Saint-Germain': 'https://upload.wikimedia.org/wikipedia/commons/thumb/8/86/Paris_Saint-Germain_Logo.svg/200px-Paris_Saint-Germain_Logo.svg.png'
}

async function buscarLogosAutomaticamente() {
  try {
    console.log("🔍 Buscando times sem logos...")
    await initializeDatabase()

    // Buscar times sem logo ou com logo inválido
    const timesSemLogo = await executeQuery(`
      SELECT id, nome, nome_curto, logo_url 
      FROM times 
      WHERE logo_url IS NULL 
         OR logo_url = '' 
         OR logo_url LIKE '%crests.football-data.org%'
         OR logo_url LIKE '%logoeps.com%'
      ORDER BY nome
    `)

    console.log(`📊 Encontrados ${timesSemLogo.length} times sem logo adequado`)

    let logosAtualizados = 0

    for (const time of timesSemLogo) {
      console.log(`\n🔍 Processando: ${time.nome} (${time.nome_curto})`)
      
      let logoEncontrado = null

      // Buscar por nome exato
      if (logosConhecidos[time.nome]) {
        logoEncontrado = logosConhecidos[time.nome]
        console.log(`✅ Logo encontrado por nome exato: ${time.nome}`)
      }
      // Buscar por nome curto
      else if (logosConhecidos[time.nome_curto]) {
        logoEncontrado = logosConhecidos[time.nome_curto]
        console.log(`✅ Logo encontrado por nome curto: ${time.nome_curto}`)
      }
      // Buscar por palavras-chave
      else {
        for (const [nomeTime, logoUrl] of Object.entries(logosConhecidos)) {
          if (time.nome.toLowerCase().includes(nomeTime.toLowerCase()) || 
              nomeTime.toLowerCase().includes(time.nome.toLowerCase()) ||
              time.nome_curto.toLowerCase().includes(nomeTime.toLowerCase())) {
            logoEncontrado = logoUrl
            console.log(`✅ Logo encontrado por similaridade: ${nomeTime}`)
            break
          }
        }
      }

      if (logoEncontrado) {
        try {
          await executeQuery(
            `UPDATE times SET logo_url = ? WHERE id = ?`,
            [logoEncontrado, time.id]
          )
          console.log(`✅ Logo atualizado para ${time.nome}`)
          logosAtualizados++
        } catch (error) {
          console.error(`❌ Erro ao atualizar ${time.nome}:`, error.message)
        }
      } else {
        console.log(`⚠️ Logo não encontrado para ${time.nome}`)
      }
    }

    // Estatísticas finais
    const stats = await executeQuery(`
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN logo_url IS NOT NULL AND logo_url != '' AND logo_url NOT LIKE '%crests.football-data.org%' AND logo_url NOT LIKE '%logoeps.com%' THEN 1 END) as com_logo_bom,
        COUNT(CASE WHEN logo_url IS NULL OR logo_url = '' THEN 1 END) as sem_logo,
        COUNT(CASE WHEN logo_url LIKE '%crests.football-data.org%' OR logo_url LIKE '%logoeps.com%' THEN 1 END) as logo_ruim
      FROM times
    `)

    console.log(`\n🎉 Processo concluído!`)
    console.log(`📊 Estatísticas finais:`)
    console.log(`  Total de times: ${stats[0].total}`)
    console.log(`  Com logo bom: ${stats[0].com_logo_bom}`)
    console.log(`  Sem logo: ${stats[0].sem_logo}`)
    console.log(`  Com logo ruim: ${stats[0].logo_ruim}`)
    console.log(`  Logos atualizados nesta execução: ${logosAtualizados}`)

    return {
      success: true,
      logosAtualizados,
      stats: stats[0]
    }

  } catch (error) {
    console.error("❌ Erro ao buscar logos:", error)
    return {
      success: false,
      error: error.message
    }
  } finally {
    process.exit(0)
  }
}

buscarLogosAutomaticamente()

import { NextRequest, NextResponse } from "next/server"
import { executeQuery } from "@/lib/db"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { order_id, status, type, message } = body

    console.log("🔔 Webhook MP recebido:", { order_id, status, type, message })

    // Validações
    if (!order_id) {
      console.log("❌ Webhook MP: order_id obrigatório")
      return NextResponse.json({ 
        error: "order_id é obrigatório" 
      }, { status: 400 })
    }

    if (!status) {
      console.log("❌ Webhook MP: status obrigatório")
      return NextResponse.json({ 
        error: "status é obrigatório" 
      }, { status: 400 })
    }

    // Mapear status para o banco de dados
    let dbStatus = status.toLowerCase()
    if (status === 'PAID' || status === 'paid' || status === 'APROVADO' || status === 'aprovado') {
      dbStatus = 'pago'
    } else if (status === 'PENDING' || status === 'pending' || status === 'PENDENTE' || status === 'pendente') {
      dbStatus = 'pendente'
    } else if (status === 'CANCELLED' || status === 'cancelled' || status === 'CANCELADO' || status === 'cancelado') {
      dbStatus = 'cancelado'
    }

    console.log("💾 Atualizando status no banco:", { order_id, status, dbStatus })

    try {
      // Atualizar bilhete pelo transaction_id ou codigo
      const updateResult = await executeQuery(`
        UPDATE bilhetes 
        SET status = ?, updated_at = NOW() 
        WHERE transaction_id = ? OR codigo = ?
      `, [dbStatus, order_id, order_id])

      console.log("✅ Resultado da atualização:", updateResult)

      // Buscar bilhete atualizado para confirmar
      const bilhete = await executeQuery(`
        SELECT * FROM bilhetes 
        WHERE transaction_id = ? OR codigo = ? 
        LIMIT 1
      `, [order_id, order_id])

      if (Array.isArray(bilhete) && bilhete.length > 0) {
        console.log("✅ Bilhete encontrado e atualizado:", {
          id: bilhete[0].id,
          codigo: bilhete[0].codigo,
          status: bilhete[0].status,
          valor: bilhete[0].valor_total
        })
        
        // Resposta de sucesso no formato esperado
        return NextResponse.json({
          order_id: order_id,
          status: "PAID",
          type: "PIXOUT",
          message: "Webhook executed successfully"
        })
      } else {
        console.log("⚠️ Bilhete não encontrado para order_id:", order_id)
        
        // Mesmo assim retorna sucesso para não reenviar webhook
        return NextResponse.json({
          order_id: order_id,
          status: status,
          type: type || "PIXOUT",
          message: "Webhook received but ticket not found"
        })
      }

    } catch (dbError) {
      console.error("❌ Erro ao atualizar banco:", dbError)
      
      // Retorna erro para reenvio do webhook
      return NextResponse.json({
        error: "Database error",
        order_id: order_id,
        status: status
      }, { status: 500 })
    }

  } catch (error) {
    console.error("❌ Erro geral no webhook MP:", error)
    
    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

// Método GET para consultar transação por order_id e reenviar webhook
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const order_id = searchParams.get('order_id')
    const token = request.headers.get('token') || request.headers.get('authorization')

    console.log("🔍 GET Webhook MP:", { order_id, hasToken: !!token })

    if (!order_id) {
      return NextResponse.json({
        message: "Webhook MP endpoint is working",
        endpoint: "/api/v1/MP/webhookruntransation",
        usage: "GET ?order_id=xxx ou POST com payload",
        expected_payload: {
          order_id: "string",
          status: "PAID|PENDING|CANCELLED",
          type: "PIXOUT",
          message: "string"
        }
      })
    }

    // Buscar transação no banco
    console.log("🔍 Buscando transação no banco:", order_id)

    const bilhete = await executeQuery(`
      SELECT * FROM bilhetes
      WHERE transaction_id = ? OR codigo = ?
      LIMIT 1
    `, [order_id, order_id])

    if (!Array.isArray(bilhete) || bilhete.length === 0) {
      console.log("❌ Transação não encontrada:", order_id)

      return NextResponse.json({
        order_id: order_id,
        status: "NOTFOUND",
        type: "PIXOUT",
        message: "Transaction not found"
      }, { status: 200 })
    }

    const transaction = bilhete[0]
    console.log("✅ Transação encontrada:", {
      id: transaction.id,
      codigo: transaction.codigo,
      status: transaction.status,
      valor: transaction.valor_total
    })

    // Mapear status do banco para resposta
    let responseStatus = "PENDING"
    let message = "awaiting payment"

    if (transaction.status === 'pago') {
      responseStatus = "PAID"
      message = "Webhook executed successfully"
    } else if (transaction.status === 'cancelado') {
      responseStatus = "CANCELLED"
      message = "Transaction cancelled"
    }

    // Se a transação está paga, simular reenvio do webhook
    if (responseStatus === "PAID") {
      console.log("🔄 Reenviando webhook para transação paga:", order_id)

      // Aqui você pode implementar lógica para reenviar webhook se necessário
      // Por exemplo, chamar um endpoint interno ou atualizar logs
    }

    return NextResponse.json({
      order_id: order_id,
      status: responseStatus,
      type: "PIXOUT",
      message: message
    })

  } catch (error) {
    console.error("❌ Erro no GET webhook MP:", error)

    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

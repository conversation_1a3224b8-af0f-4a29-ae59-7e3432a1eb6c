import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST() {
  try {
    await initializeDatabase()

    console.log("🔄 Corrigindo logo do Grêmio...")

    // Logo correto do Grêmio especificado pelo usuário
    const gremioLogoCorreto = "https://crests.football-data.org/1767.png"
    
    // Verificar se a URL está funcionando
    try {
      const response = await fetch(gremioLogoCorreto, { method: 'HEAD' })
      if (!response.ok) {
        throw new Error(`URL da imagem não está funcionando: ${response.status}`)
      }
      console.log(`✅ URL da imagem verificada: ${gremioLogoCorreto} (${response.headers.get('content-length')} bytes)`)
    } catch (error) {
      console.error(`❌ Erro ao verificar URL da imagem:`, error)
      return NextResponse.json(
        { success: false, error: "URL da imagem não está acessível" },
        { status: 400 }
      )
    }

    // Buscar todos os times do Grêmio
    const gremios = await executeQuery(`
      SELECT id, nome, nome_curto, logo_url 
      FROM times 
      WHERE nome LIKE '%Grêmio%' OR nome LIKE '%Gremio%' OR nome_curto LIKE '%Grêmio%' OR nome_curto LIKE '%Gremio%'
      ORDER BY id
    `)

    console.log(`🔍 Encontrados ${gremios.length} times do Grêmio:`)
    gremios.forEach(time => {
      console.log(`  - ID: ${time.id}, Nome: ${time.nome}, Logo atual: ${time.logo_url}`)
    })

    let timesAtualizados = 0

    // Atualizar todos os times do Grêmio
    for (const gremio of gremios) {
      await executeQuery(
        `UPDATE times SET logo_url = ? WHERE id = ?`,
        [gremioLogoCorreto, gremio.id]
      )
      console.log(`✅ Grêmio atualizado - ID: ${gremio.id}, Nome: ${gremio.nome}`)
      timesAtualizados++
    }

    // Se não encontrou nenhum, criar um novo
    if (gremios.length === 0) {
      const result = await executeQuery(
        `INSERT INTO times (nome, nome_curto, logo_url, pais) VALUES (?, ?, ?, ?)`,
        ['Grêmio', 'Grêmio', gremioLogoCorreto, 'Brasil']
      )
      console.log(`✅ Novo Grêmio criado com ID: ${result.insertId}`)
      timesAtualizados++
    }

    // Verificar resultado final
    const gremiosAtualizados = await executeQuery(`
      SELECT id, nome, nome_curto, logo_url 
      FROM times 
      WHERE nome LIKE '%Grêmio%' OR nome LIKE '%Gremio%' OR nome_curto LIKE '%Grêmio%' OR nome_curto LIKE '%Gremio%'
      ORDER BY id
    `)

    console.log(`📊 Grêmios após atualização:`)
    gremiosAtualizados.forEach(time => {
      console.log(`  ✅ ID: ${time.id}, Nome: ${time.nome}`)
      console.log(`      Logo: ${time.logo_url}`)
    })

    // Verificar se há algum logo incorreto ainda sendo usado
    const logosIncorretos = await executeQuery(`
      SELECT id, nome, logo_url 
      FROM times 
      WHERE (nome LIKE '%Grêmio%' OR nome LIKE '%Gremio%') AND logo_url != ?
    `, [gremioLogoCorreto])

    if (logosIncorretos.length > 0) {
      console.log(`⚠️ Times do Grêmio com logos possivelmente incorretos:`)
      logosIncorretos.forEach(time => {
        console.log(`  - ${time.nome}: ${time.logo_url}`)
      })
    }

    return NextResponse.json({
      success: true,
      message: `${timesAtualizados} times do Grêmio atualizados com logo correto`,
      logoCorreto: gremioLogoCorreto,
      timesAtualizados: gremiosAtualizados,
      totalAtualizados: timesAtualizados
    })

  } catch (error) {
    console.error("❌ Erro ao corrigir logo do Grêmio:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

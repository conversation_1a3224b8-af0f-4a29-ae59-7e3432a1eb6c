import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST() {
  try {
    await initializeDatabase()

    console.log("🔄 Corrigindo logos - aplicando corretos para cada time...")

    // Definir logos corretos para cada time
    const correcoesLogos = [
      {
        nomes: ["Sport Club do Recife", "SC Recife"],
        nomesContain: ["Sport Club do Recife", "SC Recife"],
        logoCorreto: "https://static.wikia.nocookie.net/logopedia/images/5/54/Sport_Club_do_Recife_logo.svg/revision/latest?cb=20210318143956",
        timeCorreto: "Sport Club do Recife"
      },
      {
        nomes: ["Ceará Sporting Club", "Ceará"],
        nomesContain: ["Ceará"],
        logoCorreto: "https://crests.football-data.org/1837.png",
        timeCorreto: "Ceará"
      },
      {
        nomes: ["Sporting Clube de Portugal", "Sporting CP"],
        nomesContain: ["Sporting Clube de Portugal", "Sporting CP"],
        logoCorreto: "https://logos-world.net/wp-content/uploads/2020/06/Sporting-CP-Logo.png",
        timeCorreto: "Sporting CP"
      },
      {
        nomes: ["Sport Lisboa e Benfica", "SL Benfica"],
        nomesContain: ["Benfica"],
        logoCorreto: "https://logos-world.net/wp-content/uploads/2020/06/Benfica-Logo.png",
        timeCorreto: "Benfica"
      }
    ]

    let totalCorrigidos = 0

    for (const correcao of correcoesLogos) {
      console.log(`\n🎯 Corrigindo ${correcao.timeCorreto}...`)

      // Buscar times que correspondem
      let condicaoWhere = correcao.nomesContain.map(nome => `nome LIKE '%${nome}%' OR nome_curto LIKE '%${nome}%'`).join(' OR ')
      
      const times = await executeQuery(`
        SELECT id, nome, nome_curto, logo_url 
        FROM times 
        WHERE ${condicaoWhere}
        ORDER BY id
      `)

      console.log(`🔍 Encontrados ${times.length} times para ${correcao.timeCorreto}:`)
      times.forEach(time => {
        console.log(`  - ID: ${time.id}, Nome: ${time.nome}`)
      })

      // Atualizar cada time
      for (const time of times) {
        await executeQuery(
          `UPDATE times SET logo_url = ? WHERE id = ?`,
          [correcao.logoCorreto, time.id]
        )
        console.log(`✅ ${time.nome} (ID: ${time.id}) atualizado`)
        totalCorrigidos++
      }
    }

    // Verificar resultado final para o Sport especificamente
    const sportFinal = await executeQuery(`
      SELECT id, nome, nome_curto, logo_url 
      FROM times 
      WHERE nome LIKE '%Sport Club do Recife%' OR nome LIKE '%SC Recife%' OR (nome_curto = 'Sport' AND nome LIKE '%Recife%')
      ORDER BY id
    `)

    console.log(`\n📊 Sport Club do Recife após correção:`)
    sportFinal.forEach(time => {
      console.log(`  ✅ ID: ${time.id}, Nome: ${time.nome}`)
      console.log(`      Logo: ${time.logo_url}`)
    })

    return NextResponse.json({
      success: true,
      message: `${totalCorrigidos} times corrigidos com logos apropriados`,
      sportFinal,
      totalCorrigidos
    })

  } catch (error) {
    console.error("❌ Erro ao corrigir logos:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

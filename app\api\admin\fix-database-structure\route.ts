import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export const dynamic = 'force-dynamic'

export async function POST() {
  try {
    await initializeDatabase()

    console.log("🔧 Verificando e corrigindo estrutura do banco de dados...")

    const fixes = []

    // 1. Verificar se tabela afiliados existe
    try {
      await executeQuery("SELECT 1 FROM afiliados LIMIT 1")
      fixes.push("✅ Tabela afiliados existe")
    } catch (error) {
      console.log("📝 Criando tabela afiliados...")
      await executeQuery(`
        CREATE TABLE IF NOT EXISTS afiliados (
          id INT AUTO_INCREMENT PRIMARY KEY,
          usuario_id INT,
          codigo_afiliado VARCHAR(50) UNIQUE,
          percentual_comissao DECIMAL(5,2) DEFAULT 10.00,
          comissao_total DECIMAL(10,2) DEFAULT 0.00,
          status ENUM('ativo', 'inativo') DEFAULT 'ativo',
          data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE
        )
      `)
      fixes.push("✅ Tabela afiliados criada")
    }

    // 2. Verificar se tabela boloes existe
    try {
      await executeQuery("SELECT 1 FROM boloes LIMIT 1")
      fixes.push("✅ Tabela boloes existe")
    } catch (error) {
      console.log("📝 Criando tabela boloes...")
      await executeQuery(`
        CREATE TABLE IF NOT EXISTS boloes (
          id INT AUTO_INCREMENT PRIMARY KEY,
          nome VARCHAR(255) NOT NULL,
          descricao TEXT,
          valor_aposta DECIMAL(10,2) NOT NULL,
          max_participantes INT DEFAULT 100,
          participantes INT DEFAULT 0,
          total_arrecadado DECIMAL(10,2) DEFAULT 0.00,
          status ENUM('ativo', 'inativo', 'finalizado') DEFAULT 'ativo',
          data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          data_encerramento DATETIME,
          created_by INT,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (created_by) REFERENCES usuarios(id)
        )
      `)
      fixes.push("✅ Tabela boloes criada")
    }

    // 3. Verificar se coluna afiliado_id existe na tabela usuarios
    try {
      await executeQuery("SELECT afiliado_id FROM usuarios LIMIT 1")
      fixes.push("✅ Coluna afiliado_id existe na tabela usuarios")
    } catch (error) {
      console.log("📝 Adicionando coluna afiliado_id na tabela usuarios...")
      await executeQuery(`
        ALTER TABLE usuarios 
        ADD COLUMN afiliado_id INT NULL,
        ADD FOREIGN KEY (afiliado_id) REFERENCES afiliados(id) ON DELETE SET NULL
      `)
      fixes.push("✅ Coluna afiliado_id adicionada na tabela usuarios")
    }

    // 4. Verificar se coluna tipo existe na tabela usuarios
    try {
      await executeQuery("SELECT tipo FROM usuarios LIMIT 1")
      fixes.push("✅ Coluna tipo existe na tabela usuarios")
    } catch (error) {
      console.log("📝 Adicionando coluna tipo na tabela usuarios...")
      await executeQuery(`
        ALTER TABLE usuarios 
        ADD COLUMN tipo ENUM('usuario', 'cambista', 'admin') DEFAULT 'usuario'
      `)
      fixes.push("✅ Coluna tipo adicionada na tabela usuarios")
    }

    // 5. Verificar se coluna status existe na tabela usuarios
    try {
      await executeQuery("SELECT status FROM usuarios LIMIT 1")
      fixes.push("✅ Coluna status existe na tabela usuarios")
    } catch (error) {
      console.log("📝 Adicionando coluna status na tabela usuarios...")
      await executeQuery(`
        ALTER TABLE usuarios 
        ADD COLUMN status ENUM('ativo', 'inativo', 'bloqueado') DEFAULT 'ativo'
      `)
      fixes.push("✅ Coluna status adicionada na tabela usuarios")
    }

    // 6. Verificar se tabela jogos existe
    try {
      await executeQuery("SELECT 1 FROM jogos LIMIT 1")
      fixes.push("✅ Tabela jogos existe")
    } catch (error) {
      console.log("📝 Criando tabela jogos...")
      await executeQuery(`
        CREATE TABLE IF NOT EXISTS jogos (
          id INT AUTO_INCREMENT PRIMARY KEY,
          campeonato_id INT,
          time_casa VARCHAR(255),
          time_visitante VARCHAR(255),
          data_jogo DATETIME,
          status ENUM('agendado', 'ao_vivo', 'finalizado', 'cancelado') DEFAULT 'agendado',
          placar_casa INT DEFAULT 0,
          placar_visitante INT DEFAULT 0,
          odd_casa DECIMAL(5,2),
          odd_empate DECIMAL(5,2),
          odd_visitante DECIMAL(5,2),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `)
      fixes.push("✅ Tabela jogos criada")
    }

    // 7. Verificar se tabela apostas existe
    try {
      await executeQuery("SELECT 1 FROM apostas LIMIT 1")
      fixes.push("✅ Tabela apostas existe")
    } catch (error) {
      console.log("📝 Criando tabela apostas...")
      await executeQuery(`
        CREATE TABLE IF NOT EXISTS apostas (
          id INT AUTO_INCREMENT PRIMARY KEY,
          usuario_id INT,
          jogo_id INT,
          palpite ENUM('casa', 'empate', 'visitante'),
          valor_aposta DECIMAL(10,2),
          odd DECIMAL(5,2),
          valor_possivel_ganho DECIMAL(10,2),
          status ENUM('pendente', 'ganhou', 'perdeu', 'cancelada') DEFAULT 'pendente',
          data_aposta TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE,
          FOREIGN KEY (jogo_id) REFERENCES jogos(id) ON DELETE CASCADE
        )
      `)
      fixes.push("✅ Tabela apostas criada")
    }

    // 8. Verificar se tabela campeonatos existe
    try {
      await executeQuery("SELECT 1 FROM campeonatos LIMIT 1")
      fixes.push("✅ Tabela campeonatos existe")
    } catch (error) {
      console.log("📝 Criando tabela campeonatos...")
      await executeQuery(`
        CREATE TABLE IF NOT EXISTS campeonatos (
          id INT AUTO_INCREMENT PRIMARY KEY,
          nome VARCHAR(255) NOT NULL,
          codigo VARCHAR(50),
          pais VARCHAR(100),
          temporada VARCHAR(20),
          status ENUM('ativo', 'inativo') DEFAULT 'ativo',
          logo_url VARCHAR(500),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `)
      fixes.push("✅ Tabela campeonatos criada")
    }

    // 9. Inserir dados básicos se não existirem
    const [adminUser] = await executeQuery("SELECT id FROM usuarios WHERE tipo = 'admin' LIMIT 1")
    if (!adminUser) {
      await executeQuery(`
        INSERT INTO usuarios (nome, email, senha, tipo, status) 
        VALUES ('Admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'ativo')
      `)
      fixes.push("✅ Usuário admin criado (senha: password)")
    }

    // 10. Verificar estrutura da tabela bilhetes
    try {
      await executeQuery("SELECT usuario_nome, usuario_email, usuario_cpf FROM bilhetes LIMIT 1")
      fixes.push("✅ Colunas de usuário existem na tabela bilhetes")
    } catch (error) {
      console.log("📝 Adicionando colunas de usuário na tabela bilhetes...")
      try {
        await executeQuery(`
          ALTER TABLE bilhetes 
          ADD COLUMN usuario_nome VARCHAR(255),
          ADD COLUMN usuario_email VARCHAR(255),
          ADD COLUMN usuario_cpf VARCHAR(20)
        `)
        fixes.push("✅ Colunas de usuário adicionadas na tabela bilhetes")
      } catch (alterError) {
        fixes.push("⚠️ Erro ao adicionar colunas na tabela bilhetes (podem já existir)")
      }
    }

    return NextResponse.json({
      success: true,
      message: "Estrutura do banco de dados verificada e corrigida",
      fixes: fixes,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error("❌ Erro ao corrigir estrutura do banco:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        message: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: "Use POST para corrigir a estrutura do banco de dados",
    endpoint: "/api/admin/fix-database-structure",
    method: "POST"
  })
}

import { initializeDatabase, executeQuery } from '../lib/database-config.js'

async function checkCampeonatosStructure() {
  try {
    await initializeDatabase()
    
    console.log("📊 Verificando estrutura da tabela campeonatos...")
    
    // Verificar estrutura da tabela
    const structure = await executeQuery('DESCRIBE campeonatos')
    
    console.log("📋 Estrutura da tabela campeonatos:")
    structure.forEach(col => {
      console.log(`   - ${col.Field}: ${col.Type} ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${col.Key} ${col.Default || ''} ${col.Extra || ''}`)
    })
    
    // Verificar dados existentes
    const count = await executeQuery('SELECT COUNT(*) as total FROM campeonatos')
    console.log(`\n📊 Total de campeonatos: ${count[0].total}`)
    
    if (count[0].total > 0) {
      const sample = await executeQuery('SELECT * FROM campeonatos LIMIT 3')
      console.log("\n📋 Amostra de dados:")
      sample.forEach(camp => {
        console.log(`   - ID: ${camp.id}, Nome: ${camp.nome}`)
      })
    }
    
    // Verificar se coluna codigo existe
    const hasCodigoColumn = structure.some(col => col.Field === 'codigo')
    
    if (!hasCodigoColumn) {
      console.log("\n⚠️ Coluna 'codigo' não existe. Adicionando...")
      await executeQuery('ALTER TABLE campeonatos ADD COLUMN codigo VARCHAR(10) AFTER nome')
      console.log("✅ Coluna 'codigo' adicionada")
    } else {
      console.log("\n✅ Coluna 'codigo' já existe")
    }
    
    // Verificar se coluna api_id existe
    const hasApiIdColumn = structure.some(col => col.Field === 'api_id')

    if (!hasApiIdColumn) {
      console.log("\n⚠️ Coluna 'api_id' não existe. Adicionando...")
      await executeQuery('ALTER TABLE campeonatos ADD COLUMN api_id VARCHAR(50) AFTER codigo')
      console.log("✅ Coluna 'api_id' adicionada")
    } else {
      console.log("\n✅ Coluna 'api_id' já existe")
    }

    // Verificar se coluna temporada_atual existe
    const hasTemporadaAtualColumn = structure.some(col => col.Field === 'temporada_atual')

    if (!hasTemporadaAtualColumn) {
      console.log("\n⚠️ Coluna 'temporada_atual' não existe. Adicionando...")
      await executeQuery('ALTER TABLE campeonatos ADD COLUMN temporada_atual VARCHAR(50) AFTER temporada')
      console.log("✅ Coluna 'temporada_atual' adicionada")
    } else {
      console.log("\n✅ Coluna 'temporada_atual' já existe")
    }
    
    console.log("\n✅ Verificação concluída!")
    
  } catch (error) {
    console.error("❌ Erro:", error)
  } finally {
    process.exit(0)
  }
}

checkCampeonatosStructure()

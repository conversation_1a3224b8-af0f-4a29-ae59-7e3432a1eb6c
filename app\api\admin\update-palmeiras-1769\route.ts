import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"
import fs from 'fs'
import path from 'path'

export async function POST() {
  try {
    await initializeDatabase()

    console.log("🔄 Atualizando Palmeiras com imagem 1769.png...")

    const imageId = "1769"
    const imageUrl = "https://crests.football-data.org/1769.png"
    const fileName = `${imageId}.png`
    const imagesDir = path.join(process.cwd(), 'public', 'images', 'teams')
    const filePath = path.join(imagesDir, fileName)
    const localPath = `/images/teams/${fileName}`

    // Baixar imagem se não existir
    if (!fs.existsSync(filePath)) {
      console.log(`📥 Baixando imagem 1769.png para Palmeiras...`)
      
      try {
        const response = await fetch(imageUrl)
        if (response.ok) {
          const buffer = await response.arrayBuffer()
          fs.writeFileSync(filePath, Buffer.from(buffer))
          console.log(`✅ Imagem salva: ${fileName} (${buffer.byteLength} bytes)`)
        } else {
          throw new Error(`Erro HTTP: ${response.status}`)
        }
      } catch (error) {
        console.error(`❌ Erro ao baixar imagem:`, error)
        return NextResponse.json(
          { success: false, error: "Erro ao baixar imagem" },
          { status: 500 }
        )
      }
    } else {
      console.log(`✅ Imagem 1769.png já existe`)
    }

    // Buscar todos os times do Palmeiras
    const palmeiras = await executeQuery(`
      SELECT id, nome, nome_curto, logo_url, image_id 
      FROM times 
      WHERE nome LIKE '%Palmeiras%' OR nome_curto LIKE '%Palmeiras%'
      ORDER BY id
    `)

    console.log(`🔍 Encontrados ${palmeiras.length} times do Palmeiras:`)
    palmeiras.forEach(time => {
      console.log(`  - ID: ${time.id}, Nome: ${time.nome}, Logo atual: ${time.logo_url}, Image ID: ${time.image_id}`)
    })

    let timesAtualizados = 0

    // Atualizar todos os times do Palmeiras
    for (const time of palmeiras) {
      await executeQuery(
        `UPDATE times SET logo_url = ?, image_id = ? WHERE id = ?`,
        [localPath, imageId, time.id]
      )
      console.log(`✅ Palmeiras atualizado - ID: ${time.id}, Nome: ${time.nome}`)
      console.log(`   Logo: ${localPath}`)
      console.log(`   Image ID: ${imageId}`)
      timesAtualizados++
    }

    // Verificar resultado final
    const palmeirasAtualizados = await executeQuery(`
      SELECT id, nome, nome_curto, logo_url, image_id 
      FROM times 
      WHERE nome LIKE '%Palmeiras%' OR nome_curto LIKE '%Palmeiras%'
      ORDER BY id
    `)

    console.log(`📊 Palmeiras após atualização:`)
    palmeirasAtualizados.forEach(time => {
      console.log(`  ✅ ID: ${time.id}, Nome: ${time.nome}`)
      console.log(`      Logo: ${time.logo_url}`)
      console.log(`      Image ID: ${time.image_id}`)
    })

    // Verificar se o arquivo existe fisicamente
    const fileExists = fs.existsSync(filePath)
    const fileSize = fileExists ? fs.statSync(filePath).size : 0

    console.log(`\n📁 Arquivo físico:`)
    console.log(`  Caminho: ${filePath}`)
    console.log(`  Existe: ${fileExists ? 'SIM' : 'NÃO'}`)
    console.log(`  Tamanho: ${fileSize} bytes`)

    return NextResponse.json({
      success: true,
      message: `${timesAtualizados} times do Palmeiras atualizados com 1769.png`,
      imageId,
      localPath,
      timesAtualizados: palmeirasAtualizados,
      totalAtualizados: timesAtualizados,
      fileInfo: {
        path: filePath,
        exists: fileExists,
        size: fileSize
      }
    })

  } catch (error) {
    console.error("❌ Erro ao atualizar Palmeiras:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST() {
  try {
    await initializeDatabase()

    console.log("🔧 Correção final dos logos problemáticos...")

    // Corrigir especificamente os logos que ainda estão com URLs antigas
    const correcoes = [
      {
        url_antiga: 'https://crests.football-data.org/6692.png',
        url_nova: 'https://upload.wikimedia.org/wikipedia/commons/thumb/c/c2/Atletico_Goianiense.png/200px-Atletico_Goianiense.png',
        nome: 'Atlético-GO'
      },
      {
        url_antiga: 'https://logoeps.com/wp-content/uploads/2013/03/atletico-mineiro-vector-logo.png',
        url_nova: 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/5f/Atletico_mineiro_galo.png/200px-Atletico_mineiro_galo.png',
        nome: 'Atlético-MG'
      },
      {
        url_antiga: 'https://logoeps.com/wp-content/uploads/2013/03/cruzeiro-vector-logo.png',
        url_nova: 'https://upload.wikimedia.org/wikipedia/commons/thumb/9/90/Cruzeiro_Esporte_Clube_%28logo%29.svg/200px-Cruzeiro_Esporte_Clube_%28logo%29.svg.png',
        nome: 'Cruzeiro'
      }
    ]

    let totalCorrigido = 0

    for (const correcao of correcoes) {
      console.log(`🔄 Corrigindo ${correcao.nome}...`)
      
      const result = await executeQuery(
        `UPDATE times SET logo_url = ? WHERE logo_url = ?`,
        [correcao.url_nova, correcao.url_antiga]
      )

      if (result.affectedRows > 0) {
        console.log(`✅ ${correcao.nome}: ${result.affectedRows} registros corrigidos`)
        totalCorrigido += result.affectedRows
      } else {
        console.log(`⚠️ ${correcao.nome}: Nenhum registro encontrado com URL antiga`)
      }
    }

    // Corrigir também por nome específico para garantir
    const correcoesPorNome = [
      {
        nome_like: '%Atlético%Goian%',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/c/c2/Atletico_Goianiense.png/200px-Atletico_Goianiense.png'
      },
      {
        nome_like: '%Atlético%Mineiro%',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/5f/Atletico_mineiro_galo.png/200px-Atletico_mineiro_galo.png'
      }
    ]

    for (const correcao of correcoesPorNome) {
      const result = await executeQuery(
        `UPDATE times SET logo_url = ? WHERE nome LIKE ?`,
        [correcao.logo, correcao.nome_like]
      )

      if (result.affectedRows > 0) {
        console.log(`✅ Correção por nome: ${result.affectedRows} registros atualizados`)
        totalCorrigido += result.affectedRows
      }
    }

    // Verificar resultado final
    const verificacao = await executeQuery(`
      SELECT nome, nome_curto, logo_url 
      FROM times 
      WHERE nome LIKE '%Atlético%' OR nome LIKE '%Cruzeiro%'
      ORDER BY nome
    `)

    console.log("📊 Verificação final dos times Atlético:")
    verificacao.forEach(time => {
      const logoStatus = time.logo_url?.includes('upload.wikimedia.org') ? '✅' : '❌'
      console.log(`  ${logoStatus} ${time.nome} (${time.nome_curto})`)
      console.log(`      ${time.logo_url}`)
    })

    return NextResponse.json({
      success: true,
      message: `${totalCorrigido} logos corrigidos na correção final`,
      timesVerificados: verificacao
    })

  } catch (error) {
    console.error("❌ Erro na correção final:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

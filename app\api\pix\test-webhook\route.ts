import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { transaction_id, status = 'PAID' } = await request.json()

    if (!transaction_id) {
      return NextResponse.json(
        { error: 'transaction_id é obrigatório' },
        { status: 400 }
      )
    }

    console.log('🧪 Testando webhook PIX:', { transaction_id, status })

    // Simular dados de webhook conforme documentação
    const webhookData = {
      qr_code_payment_id: `qr_${transaction_id}`,
      transaction_id: transaction_id,
      order_id: `order_${transaction_id}`,
      amount: "1.00",
      description: "Teste de webhook PIX",
      status: status,
      end_to_end_id: `E12345678202407081234567890123456`,
      last_updated_at: new Date().toISOString(),
      error: status === 'FAILED' ? 'Erro simulado para teste' : null
    }

    console.log('📤 Enviando webhook de teste:', webhookData)

    // Enviar para o endpoint webhook
    const webhookResponse = await fetch(`${request.nextUrl.origin}/api/pix/webhook`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(webhookData)
    })

    const webhookResult = await webhookResponse.json()

    console.log('📥 Resposta do webhook:', {
      status: webhookResponse.status,
      result: webhookResult
    })

    return NextResponse.json({
      success: true,
      message: 'Webhook de teste enviado',
      webhook_data: webhookData,
      webhook_response: {
        status: webhookResponse.status,
        data: webhookResult
      }
    })

  } catch (error) {
    console.error('❌ Erro no teste de webhook:', error)
    return NextResponse.json(
      { 
        error: 'Erro interno do servidor',
        message: 'Falha ao testar webhook'
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Endpoint para testar webhook PIX',
    usage: {
      method: 'POST',
      body: {
        transaction_id: 'string (obrigatório)',
        status: 'string (opcional, padrão: PAID)'
      },
      example: {
        transaction_id: 'TXN123456789',
        status: 'PAID'
      }
    },
    available_status: [
      'PAID',
      'PENDING', 
      'GENERATED',
      'FAILED',
      'PROCESSING',
      'WAITING'
    ]
  })
}

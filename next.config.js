/** @type {import('next').NextConfig} */
const nextConfig = {
  // Configurações para resolver warnings de cross-origin
  allowedDevOrigins: [
    'ouroemu.site',
    'www.ouroemu.site',
    'localhost:3000'
  ],
  
  // Configurações de headers para CORS
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET, POST, PUT, DELETE, OPTIONS' },
          { key: 'Access-Control-Allow-Headers', value: 'Content-Type, Authorization' },
        ],
      },
    ]
  },

  // Configurações de imagens para permitir logos de times
  images: {
    domains: [
      'crests.football-data.org',
      'logos.footapi.com',
      'media.api-sports.io',
      'ouroemu.site'
    ],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'crests.football-data.org',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'logos.footapi.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'media.api-sports.io',
        port: '',
        pathname: '/**',
      }
    ]
  },

  // Configurações de webpack para otimização
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      }
    }
    return config
  },

  // Configurações de experimental features
  experimental: {
    serverComponentsExternalPackages: ['mysql2']
  },

  // Configurações de output para produção
  output: 'standalone',
  
  // Configurações de compressão
  compress: true,
  
  // Configurações de cache
  onDemandEntries: {
    maxInactiveAge: 25 * 1000,
    pagesBufferLength: 2,
  }
}

module.exports = nextConfig

import { executeQuery, initializeDatabase } from '../lib/database-config.js'

async function fixBilhetesConstraint() {
  try {
    console.log('🔧 Inicializando conexão com o banco...')
    await initializeDatabase()

    console.log('👥 Verificando usuários existentes...')
    const usuarios = await executeQuery('SELECT id, nome, email, status FROM usuarios ORDER BY id')
    
    if (usuarios.length === 0) {
      console.log('⚠️ Nenhum usuário encontrado! Criando usuário padrão...')
      
      // Criar usuário padrão
      const result = await executeQuery(`
        INSERT INTO usuarios (nome, email, telefone, cpf_cnpj, senha_hash, tipo, status, saldo, data_cadastro)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
      `, [
        'Usuário <PERSON>drão',
        '<EMAIL>',
        '11999999999',
        '12345678901',
        '$2b$10$defaulthash',
        'usuario',
        'ativo',
        0.00
      ])
      
      console.log('✅ Usuário padrão criado com ID:', result.insertId)
    } else {
      console.log('👥 Usuários encontrados:')
      usuarios.forEach(user => {
        console.log(`   - ID: ${user.id}, Nome: ${user.nome}, Email: ${user.email}, Status: ${user.status}`)
      })
    }

    console.log('\n🎫 Verificando bilhetes órfãos...')
    const bilhetesOrfaos = await executeQuery(`
      SELECT b.id, b.codigo, b.usuario_id 
      FROM bilhetes b 
      LEFT JOIN usuarios u ON b.usuario_id = u.id 
      WHERE u.id IS NULL
    `)
    
    if (bilhetesOrfaos.length > 0) {
      console.log('⚠️ Bilhetes órfãos encontrados:')
      bilhetesOrfaos.forEach(bilhete => {
        console.log(`   - Bilhete ID: ${bilhete.id}, Código: ${bilhete.codigo}, Usuario ID inválido: ${bilhete.usuario_id}`)
      })

      // Obter o primeiro usuário válido
      const primeiroUsuario = usuarios[0]
      if (primeiroUsuario) {
        console.log(`🔧 Corrigindo bilhetes órfãos para usuario_id: ${primeiroUsuario.id}`)
        
        for (const bilhete of bilhetesOrfaos) {
          await executeQuery(`
            UPDATE bilhetes 
            SET usuario_id = ? 
            WHERE id = ?
          `, [primeiroUsuario.id, bilhete.id])
          
          console.log(`   ✅ Bilhete ${bilhete.codigo} corrigido`)
        }
      }
    } else {
      console.log('✅ Nenhum bilhete órfão encontrado')
    }

    console.log('\n🔧 Verificando constraint da tabela bilhetes...')
    
    // Verificar se a constraint permite NULL
    const tableInfo = await executeQuery('DESCRIBE bilhetes')
    const usuarioIdField = tableInfo.find(field => field.Field === 'usuario_id')
    
    if (usuarioIdField) {
      console.log(`📋 Campo usuario_id: ${usuarioIdField.Type}, NULL: ${usuarioIdField.Null}, Default: ${usuarioIdField.Default}`)
      
      if (usuarioIdField.Null === 'NO') {
        console.log('⚠️ Campo usuario_id não permite NULL, mas deveria para flexibilidade')
        console.log('💡 Recomendação: Alterar para permitir NULL ou sempre validar user_id antes de inserir')
      }
    }

    console.log('\n✅ Verificação e correção concluídas!')
    console.log('\n📝 Próximos passos:')
    console.log('   1. Sempre validar se o usuário está logado antes de criar bilhetes')
    console.log('   2. Validar se o user_id existe na tabela usuarios antes de inserir bilhetes')
    console.log('   3. Considerar usar um usuário padrão para bilhetes anônimos')

  } catch (error) {
    console.error('❌ Erro durante a correção:', error)
    process.exit(1)
  }
}

fixBilhetesConstraint()

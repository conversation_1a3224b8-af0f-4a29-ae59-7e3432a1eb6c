<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Webhook Mercado Pago</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .result { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
        .success { border-left-color: #28a745; background: #d4edda; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        button { background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; font-size: 14px; }
        button:hover { background: #0056b3; }
        input, select { padding: 10px; margin: 5px; width: 300px; border: 1px solid #ddd; border-radius: 5px; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        h1 { color: #333; text-align: center; }
        h3 { color: #555; border-bottom: 2px solid #007bff; padding-bottom: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Teste Webhook Mercado Pago</h1>
        
        <div class="section">
            <h3>1. Status do Webhook</h3>
            <button onclick="checkWebhookStatus()">🔍 Verificar Status</button>
            <div id="statusResult" class="result"></div>
        </div>

        <div class="section">
            <h3>2. Testar Webhook</h3>
            <input type="text" id="bilheteCodigo" placeholder="Código do bilhete" value="BLT17526153235061OKU7QD">
            <br>
            <select id="statusPagamento">
                <option value="PAID">PAID (Pago)</option>
                <option value="PENDING">PENDING (Pendente)</option>
                <option value="CANCELLED">CANCELLED (Cancelado)</option>
            </select>
            <br>
            <button onclick="testWebhook()">🚀 Enviar Webhook</button>
            <div id="webhookResult" class="result"></div>
        </div>

        <div class="section">
            <h3>3. Consultar Status do Bilhete</h3>
            <input type="text" id="consultaBilhete" placeholder="Código do bilhete" value="BLT17526153235061OKU7QD">
            <button onclick="consultarStatus()">📊 Consultar Status</button>
            <div id="consultaResult" class="result"></div>
        </div>
    </div>

    <script>
        async function checkWebhookStatus() {
            const resultDiv = document.getElementById('statusResult');
            resultDiv.innerHTML = '⏳ Verificando status...';
            
            try {
                const response = await fetch('/api/v1/MP/webhookruntransation');
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <h4>✅ Webhook Funcionando</h4>
                    <p><strong>Status:</strong> ${data.status}</p>
                    <p><strong>Endpoint:</strong> ${data.endpoint}</p>
                    <p><strong>Estatísticas:</strong></p>
                    <pre>${JSON.stringify(data.estatisticas, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<h4>❌ Erro</h4><p>${error.message}</p>`;
            }
        }

        async function testWebhook() {
            const bilheteCodigo = document.getElementById('bilheteCodigo').value;
            const status = document.getElementById('statusPagamento').value;
            const resultDiv = document.getElementById('webhookResult');
            
            if (!bilheteCodigo) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '<h4>❌ Erro</h4><p>Digite o código do bilhete</p>';
                return;
            }
            
            resultDiv.innerHTML = '⏳ Enviando webhook...';
            
            const payload = {
                order_id: bilheteCodigo,
                status: status,
                type: "PIXOUT",
                message: "Payment processed successfully"
            };
            
            try {
                const response = await fetch('/api/v1/MP/webhookruntransation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(payload)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h4>✅ Webhook Processado</h4>
                        <p><strong>Status:</strong> ${data.status}</p>
                        <p><strong>Processado:</strong> ${data.processed_successfully ? 'Sim' : 'Não'}</p>
                        <p><strong>Bilhete:</strong> ${data.bilhete_codigo}</p>
                        <p><strong>Valor:</strong> R$ ${data.valor}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    
                    if (data.status === 'pago') {
                        resultDiv.innerHTML += '<p style="color: green; font-weight: bold;">🎉 SUCESSO! Bilhete marcado como PAGO!</p>';
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h4>❌ Erro no Webhook</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<h4>❌ Erro</h4><p>${error.message}</p>`;
            }
        }

        async function consultarStatus() {
            const bilheteCodigo = document.getElementById('consultaBilhete').value;
            const resultDiv = document.getElementById('consultaResult');
            
            if (!bilheteCodigo) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '<h4>❌ Erro</h4><p>Digite o código do bilhete</p>';
                return;
            }
            
            resultDiv.innerHTML = '⏳ Consultando status...';
            
            try {
                const response = await fetch(`/api/webhook/status?order_id=${bilheteCodigo}`);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h4>✅ Status do Bilhete</h4>
                        <p><strong>Código:</strong> ${data.bilhete?.codigo}</p>
                        <p><strong>Status:</strong> ${data.status}</p>
                        <p><strong>Pago:</strong> ${data.payment_info?.is_paid ? 'Sim' : 'Não'}</p>
                        <p><strong>Pendente:</strong> ${data.payment_info?.is_pending ? 'Sim' : 'Não'}</p>
                        <p><strong>Valor:</strong> R$ ${data.bilhete?.valor}</p>
                        <p><strong>Usuário:</strong> ${data.bilhete?.usuario_nome}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h4>❌ Bilhete não encontrado</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<h4>❌ Erro</h4><p>${error.message}</p>`;
            }
        }

        // Carregar status inicial
        window.onload = function() {
            checkWebhookStatus();
        };
    </script>
</body>
</html>

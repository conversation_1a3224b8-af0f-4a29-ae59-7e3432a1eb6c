-- Script para limpar dados de campeonatos e configurações
-- ATENÇÃO: Este script remove TODOS os dados das tabelas especificadas

USE `sistema-bolao-top`;

-- Verificar dados antes da limpeza
SELECT 'ANTES DA LIMPEZA - Contagem de registros:' as info;
SELECT 'campeonatos' as tabela, COUNT(*) as registros FROM campeonatos;
SELECT 'configuracoes' as tabela, COUNT(*) as registros FROM configuracoes;

-- Desabilitar verificações de foreign key temporariamente
SET FOREIGN_KEY_CHECKS = 0;

-- Limpar tabela de jogos que dependem de campeonatos
DELETE FROM jogos WHERE campeonato_id IN (SELECT id FROM campeonatos);
SELECT 'jogos relacionados a campeonatos removidos' as status;

-- Limpar tabela bolao_jogos que pode ter referências
DELETE FROM bolao_jogos WHERE jogo_id NOT IN (SELECT id FROM jogos);
SELECT 'bolao_jogos órfãos removidos' as status;

-- Limpar dados de campeonatos
DELETE FROM campeonatos;
SELECT 'Tabela campeonatos limpa' as status;

-- Limpar dados de configurações
DELETE FROM configuracoes;
SELECT 'Tabela configuracoes limpa' as status;

-- Reabilitar verificações de foreign key
SET FOREIGN_KEY_CHECKS = 1;

-- Verificar dados após a limpeza
SELECT 'APÓS A LIMPEZA - Contagem de registros:' as info;
SELECT 'campeonatos' as tabela, COUNT(*) as registros FROM campeonatos;
SELECT 'configuracoes' as tabela, COUNT(*) as registros FROM configuracoes;
SELECT 'jogos' as tabela, COUNT(*) as registros FROM jogos;

SELECT 'Limpeza concluída com sucesso!' as resultado;

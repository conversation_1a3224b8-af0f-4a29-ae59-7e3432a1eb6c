import { executeQuery, initializeDatabase } from '../lib/database-config.js'

async function verifyUser18() {
  try {
    console.log('🔧 Inicializando conexão com o banco...')
    await initializeDatabase()

    console.log('👥 Verificando usuário ID 18...')
    const user18 = await executeQuery('SELECT * FROM usuarios WHERE id = ?', [18])
    
    if (user18.length > 0) {
      console.log('✅ Usuário ID 18 encontrado:')
      console.log('   - ID:', user18[0].id)
      console.log('   - Nome:', user18[0].nome)
      console.log('   - Email:', user18[0].email)
      console.log('   - Status:', user18[0].status)
      console.log('   - Data cadastro:', user18[0].data_cadastro)
    } else {
      console.log('❌ Usuário ID 18 NÃO encontrado!')
    }

    console.log('\n👥 Listando todos os usuários:')
    const allUsers = await executeQuery('SELECT id, nome, email, status FROM usuarios ORDER BY id')
    allUsers.forEach(user => {
      console.log(`   - ID: ${user.id}, Nome: ${user.nome}, Email: ${user.email}, Status: ${user.status}`)
    })

    console.log('\n🔍 Testando query de validação da API...')
    const validationQuery = await executeQuery('SELECT id FROM usuarios WHERE id = ?', [18])
    console.log('📊 Resultado da query de validação:', validationQuery)
    
    if (validationQuery && validationQuery.length > 0) {
      console.log('✅ Query de validação funcionando corretamente')
    } else {
      console.log('❌ Query de validação falhou!')
    }

  } catch (error) {
    console.error('❌ Erro durante a verificação:', error)
    process.exit(1)
  }
}

verifyUser18()

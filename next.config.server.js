/** @type {import('next').NextConfig} */
const nextConfig = {
  // Configurações específicas para servidor
  experimental: {
    serverComponentsExternalPackages: ['mysql2'],
  },
  
  // Configurações de build para servidor
  output: 'standalone',
  
  // Configurações de runtime
  poweredByHeader: false,
  
  // Configurações de imagens
  images: {
    domains: ['crests.football-data.org'],
    unoptimized: true
  },
  
  // Configurações de webpack para servidor
  webpack: (config, { isServer }) => {
    if (isServer) {
      config.externals.push('mysql2')
    }
    return config
  },
  
  // Configurações de ambiente
  env: {
    CUSTOM_KEY: 'server-config',
  },
}

module.exports = nextConfig

import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST() {
  try {
    await initializeDatabase()

    console.log("🔧 Corrigindo especificamente o Sport Recife...")

    // Corrigir Sport Recife especificamente
    const sportCorreto = {
      nome: 'Sport Club do Recife',
      nome_curto: 'Sport',
      logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/54/Sport_Club_do_Recife_logo.svg/200px-Sport_Club_do_Recife_logo.svg.png'
    }

    // Primeiro, verificar se existe um time com nome "Sport" ou similar
    const sportExistente = await executeQuery(
      `SELECT id, nome, nome_curto, logo_url FROM times WHERE nome LIKE '%Sport%' AND nome NOT LIKE '%Ceará%' LIMIT 5`
    )

    console.log("🔍 Times com 'Sport' encontrados:", sportExistente)

    // Se não existir, criar o Sport Recife
    if (sportExistente.length === 0) {
      console.log("➕ Criando Sport Club do Recife...")
      await executeQuery(
        `INSERT INTO times (nome, nome_curto, logo_url, pais) VALUES (?, ?, ?, ?)`,
        [sportCorreto.nome, sportCorreto.nome_curto, sportCorreto.logo, 'Brasil']
      )
      console.log("✅ Sport Club do Recife criado com sucesso")
    } else {
      // Atualizar o existente
      for (const sport of sportExistente) {
        if (sport.nome.includes('Sport') && !sport.nome.includes('Ceará')) {
          console.log(`🔄 Atualizando ${sport.nome}...`)
          await executeQuery(
            `UPDATE times SET nome = ?, nome_curto = ?, logo_url = ? WHERE id = ?`,
            [sportCorreto.nome, sportCorreto.nome_curto, sportCorreto.logo, sport.id]
          )
          console.log(`✅ ${sport.nome} atualizado para Sport Club do Recife`)
        }
      }
    }

    // Corrigir também o Ceará que pode estar com nome errado
    await executeQuery(
      `UPDATE times SET nome = 'Ceará Sporting Club', nome_curto = 'Ceará', logo_url = ? WHERE nome LIKE '%Ceará%'`,
      ['https://upload.wikimedia.org/wikipedia/commons/thumb/3/3e/Ceara_sporting_club_logo.svg/200px-Ceara_sporting_club_logo.svg.png']
    )

    console.log("✅ Ceará também corrigido")

    // Verificar resultado final
    const resultado = await executeQuery(
      `SELECT nome, nome_curto, logo_url FROM times WHERE nome LIKE '%Sport%' OR nome LIKE '%Ceará%'`
    )

    console.log("📊 Resultado final:", resultado)

    return NextResponse.json({
      success: true,
      message: "Sport Recife e Ceará corrigidos com sucesso",
      times: resultado
    })

  } catch (error) {
    console.error("❌ Erro ao corrigir Sport:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

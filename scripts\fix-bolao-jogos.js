#!/usr/bin/env node

import { initializeDatabase, executeQuery } from '../lib/database.js'

async function fixBolaoJogos() {
  try {
    console.log('🔧 Corrigindo bolão sem jogos...')
    
    await initializeDatabase()
    
    // Buscar bolão ativo sem jogos
    const boloes = await executeQuery(`
      SELECT b.*, COUNT(bj.jogo_id) as total_jogos
      FROM boloes b
      LEFT JOIN bolao_jogos bj ON b.id = bj.bolao_id
      WHERE b.status = 'ativo'
      GROUP BY b.id
      HAVING total_jogos = 0
    `)
    
    if (boloes.length === 0) {
      console.log('✅ Todos os bolões ativos já têm jogos associados')
      return
    }
    
    console.log(`📋 Encontrados ${boloes.length} bolões sem jogos:`)
    boloes.forEach(bolao => {
      console.log(`   - ${bolao.nome} (ID: ${bolao.id})`)
    })
    
    // Buscar jogos disponíveis
    const jogos = await executeQuery(`
      SELECT j.*, c.nome as campeonato_nome
      FROM jogos j
      LEFT JOIN campeonatos c ON j.campeonato_id = c.id
      WHERE j.status = 'agendado' 
        AND j.data_jogo >= NOW()
      ORDER BY j.data_jogo ASC
      LIMIT 10
    `)
    
    console.log(`🎯 Encontrados ${jogos.length} jogos disponíveis`)
    
    if (jogos.length === 0) {
      console.log('⚠️ Nenhum jogo disponível para associar')
      
      // Criar alguns jogos de exemplo
      console.log('🎲 Criando jogos de exemplo...')
      
      // Buscar ou criar campeonatos
      let campeonatos = await executeQuery('SELECT * FROM campeonatos LIMIT 2')
      
      if (campeonatos.length === 0) {
        console.log('📋 Criando campeonatos de exemplo...')
        await executeQuery(`
          INSERT INTO campeonatos (nome, descricao, tipo, status) VALUES
          ('Brasileirão Série A', 'Campeonato Brasileiro Série A 2024', 'nacional', 'ativo'),
          ('Copa Libertadores', 'Copa Libertadores da América 2024', 'internacional', 'ativo')
        `)
        campeonatos = await executeQuery('SELECT * FROM campeonatos LIMIT 2')
      }
      
      // Buscar ou criar times
      let times = await executeQuery('SELECT * FROM times LIMIT 4')
      
      if (times.length < 4) {
        console.log('⚽ Criando times de exemplo...')
        await executeQuery(`
          INSERT INTO times (nome, nome_curto, logo_url) VALUES
          ('Flamengo', 'FLA', 'https://logoeps.com/wp-content/uploads/2013/03/flamengo-vector-logo.png'),
          ('Palmeiras', 'PAL', 'https://logoeps.com/wp-content/uploads/2013/03/palmeiras-vector-logo.png'),
          ('São Paulo', 'SAO', 'https://logoeps.com/wp-content/uploads/2013/03/sao-paulo-vector-logo.png'),
          ('Corinthians', 'COR', 'https://logoeps.com/wp-content/uploads/2013/03/corinthians-vector-logo.png')
          ON DUPLICATE KEY UPDATE nome = VALUES(nome)
        `)
        times = await executeQuery('SELECT * FROM times LIMIT 4')
      }
      
      // Criar jogos de exemplo
      const dataBase = new Date()
      dataBase.setDate(dataBase.getDate() + 1) // Amanhã
      
      const jogosExemplo = [
        {
          campeonato_id: campeonatos[0].id,
          time_casa_id: times[0].id,
          time_fora_id: times[1].id,
          data_jogo: new Date(dataBase.getTime() + 2 * 60 * 60 * 1000) // +2h
        },
        {
          campeonato_id: campeonatos[0].id,
          time_casa_id: times[2].id,
          time_fora_id: times[3].id,
          data_jogo: new Date(dataBase.getTime() + 4 * 60 * 60 * 1000) // +4h
        },
        {
          campeonato_id: campeonatos[1].id,
          time_casa_id: times[1].id,
          time_fora_id: times[2].id,
          data_jogo: new Date(dataBase.getTime() + 6 * 60 * 60 * 1000) // +6h
        }
      ]
      
      for (const jogo of jogosExemplo) {
        await executeQuery(`
          INSERT INTO jogos (campeonato_id, time_casa_id, time_fora_id, data_jogo, status)
          VALUES (?, ?, ?, ?, 'agendado')
        `, [jogo.campeonato_id, jogo.time_casa_id, jogo.time_fora_id, jogo.data_jogo])
      }
      
      // Buscar jogos criados
      const novosJogos = await executeQuery(`
        SELECT j.*, c.nome as campeonato_nome
        FROM jogos j
        LEFT JOIN campeonatos c ON j.campeonato_id = c.id
        WHERE j.status = 'agendado' 
          AND j.data_jogo >= NOW()
        ORDER BY j.data_jogo ASC
        LIMIT 10
      `)
      
      console.log(`✅ Criados ${novosJogos.length} jogos de exemplo`)
      jogos.push(...novosJogos)
    }
    
    // Associar jogos aos bolões
    for (const bolao of boloes) {
      console.log(`🔗 Associando jogos ao bolão "${bolao.nome}"...`)
      
      // Pegar os primeiros 5 jogos
      const jogosParaBolao = jogos.slice(0, 5)
      
      for (const jogo of jogosParaBolao) {
        await executeQuery(`
          INSERT IGNORE INTO bolao_jogos (bolao_id, jogo_id)
          VALUES (?, ?)
        `, [bolao.id, jogo.id])
      }
      
      console.log(`✅ Associados ${jogosParaBolao.length} jogos ao bolão "${bolao.nome}"`)
    }
    
    // Verificar resultado
    const boloesAtualizados = await executeQuery(`
      SELECT b.*, COUNT(bj.jogo_id) as total_jogos
      FROM boloes b
      LEFT JOIN bolao_jogos bj ON b.id = bj.bolao_id
      WHERE b.status = 'ativo'
      GROUP BY b.id
    `)
    
    console.log('\n📊 Status final dos bolões:')
    boloesAtualizados.forEach(bolao => {
      console.log(`   - ${bolao.nome}: ${bolao.total_jogos} jogos`)
    })
    
    console.log('\n✅ Correção concluída com sucesso!')
    
  } catch (error) {
    console.error('❌ Erro ao corrigir bolões:', error)
    process.exit(1)
  }
}

fixBolaoJogos()

import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST() {
  try {
    await initializeDatabase()

    console.log("🔧 Separando definitivamente Ceará e Sport...")

    // 1. Corrigir Ceará Sporting Club
    await executeQuery(
      `UPDATE times SET nome = 'Ceará Sporting Club', nome_curto = 'Ceará', logo_url = ? WHERE nome LIKE '%Ceará%'`,
      ['https://upload.wikimedia.org/wikipedia/commons/thumb/3/3e/Ceara_sporting_club_logo.svg/200px-Ceara_sporting_club_logo.svg.png']
    )

    // 2. Verificar se existe Sport Club do Recife
    const sportExistente = await executeQuery(
      `SELECT id, nome, nome_curto FROM times WHERE nome LIKE '%Sport Club do Recife%' OR (nome LIKE '%Sport%' AND nome NOT LIKE '%Ceará%')`
    )

    console.log("🔍 Sport encontrado:", sportExistente)

    if (sportExistente.length === 0) {
      // 3. Criar Sport Club do Recife se não existir
      console.log("➕ Criando Sport Club do Recife...")
      await executeQuery(
        `INSERT INTO times (nome, nome_curto, logo_url, pais) VALUES (?, ?, ?, ?)`,
        [
          'Sport Club do Recife',
          'Sport',
          'https://upload.wikimedia.org/wikipedia/commons/thumb/5/54/Sport_Club_do_Recife_logo.svg/200px-Sport_Club_do_Recife_logo.svg.png',
          'Brasil'
        ]
      )
      console.log("✅ Sport Club do Recife criado")
    } else {
      // 4. Atualizar Sport existente
      for (const sport of sportExistente) {
        if (!sport.nome.includes('Ceará')) {
          console.log(`🔄 Atualizando ${sport.nome} para Sport Club do Recife...`)
          await executeQuery(
            `UPDATE times SET nome = 'Sport Club do Recife', nome_curto = 'Sport', logo_url = ? WHERE id = ?`,
            [
              'https://upload.wikimedia.org/wikipedia/commons/thumb/5/54/Sport_Club_do_Recife_logo.svg/200px-Sport_Club_do_Recife_logo.svg.png',
              sport.id
            ]
          )
          console.log("✅ Sport atualizado")
        }
      }
    }

    // 5. Verificar resultado final
    const resultado = await executeQuery(
      `SELECT nome, nome_curto, logo_url FROM times WHERE nome LIKE '%Ceará%' OR nome LIKE '%Sport%' ORDER BY nome`
    )

    console.log("📊 Times finais:")
    resultado.forEach(time => {
      console.log(`  - ${time.nome} (${time.nome_curto})`)
    })

    return NextResponse.json({
      success: true,
      message: "Ceará e Sport separados com sucesso",
      times: resultado
    })

  } catch (error) {
    console.error("❌ Erro ao separar Ceará e Sport:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

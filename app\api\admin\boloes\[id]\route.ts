import { type NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery, executeQuerySingle } from "@/lib/database-config"

export async function PATCH(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    console.log(`🔄 Tentando atualizar bolão ID: ${params.id}`)

    const bolaoId = parseInt(params.id)
    const data = await request.json()

    // Tentar atualizar no banco com timeout
    try {
      await initializeDatabase()

      // Verificar se o bolão existe com timeout
      const bolao = await Promise.race([
        executeQuerySingle('SELECT id FROM boloes WHERE id = ?', [bolaoId]),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout na verificação do bolão')), 3000)
        )
      ])

      if (!bolao) {
        return NextResponse.json(
          {
            success: false,
            error: "Bolão não encontrado",
          },
          { status: 404 }
        )
      }

      // Atualizar status do bolão com timeout
      if (data.status) {
        await Promise.race([
          executeQuery('UPDATE boloes SET status = ? WHERE id = ?', [data.status, bolaoId]),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Timeout na atualização')), 3000)
          )
        ])

        console.log(`✅ Bolão ${bolaoId} atualizado no banco`)
        return NextResponse.json({
          success: true,
          message: `Bolão ${data.status === 'ativo' ? 'ativado' : 'desativado'} com sucesso`,
          source: 'database'
        })
      }

      return NextResponse.json(
        {
          success: false,
          error: "Dados inválidos para atualização",
        },
        { status: 400 }
      )

    } catch (dbError) {
      console.error(`❌ Erro ao atualizar bolão no banco:`, dbError.message)

      return NextResponse.json({
        success: false,
        error: 'Erro ao atualizar bolão no banco de dados',
        message: dbError.message
      }, { status: 500 })
    }
  } catch (error) {
    console.error("Erro ao atualizar bolão:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido",
      },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    console.log(`🗑️ Tentando deletar bolão ID: ${params.id}`)

    const bolaoId = parseInt(params.id)

    // Tentar deletar do banco com timeout
    try {
      await initializeDatabase()

      // Verificar se o bolão existe com timeout
      const bolao = await Promise.race([
        executeQuerySingle('SELECT id, nome FROM boloes WHERE id = ?', [bolaoId]),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout na verificação do bolão')), 3000)
        )
      ])

      if (!bolao) {
        return NextResponse.json(
          {
            success: false,
            error: "Bolão não encontrado",
          },
          { status: 404 }
        )
      }

      // Verificar se há apostas associadas com timeout
      const apostas = await Promise.race([
        executeQuerySingle('SELECT COUNT(*) as total FROM apostas WHERE bolao_id = ?', [bolaoId]),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout na verificação de apostas')), 3000)
        )
      ])

      if (apostas && apostas.total > 0) {
        return NextResponse.json(
          {
            success: false,
            error: "Não é possível deletar um bolão que já possui apostas",
          },
          { status: 400 }
        )
      }

      // Deletar o bolão com timeout
      await Promise.race([
        executeQuery('DELETE FROM boloes WHERE id = ?', [bolaoId]),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout na deleção')), 3000)
        )
      ])

      console.log(`✅ Bolão ${bolaoId} deletado do banco`)
      return NextResponse.json({
        success: true,
        message: "Bolão deletado com sucesso",
        source: 'database'
      })

    } catch (dbError) {
      console.error(`❌ Erro ao deletar bolão do banco:`, dbError.message)

      return NextResponse.json({
        success: false,
        error: 'Erro ao deletar bolão do banco de dados',
        message: dbError.message
      }, { status: 500 })
    }
  } catch (error) {
    console.error("Erro ao deletar bolão:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido",
      },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    console.log(`📝 Tentando editar bolão ID: ${params.id}`)

    const bolaoId = parseInt(params.id)
    const data = await request.json()

    // Tentar atualizar no banco com timeout
    try {
      await initializeDatabase()

      // Verificar se o bolão existe com timeout
      const bolao = await Promise.race([
        executeQuerySingle('SELECT id FROM boloes WHERE id = ?', [bolaoId]),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout na verificação do bolão')), 3000)
        )
      ])

      if (!bolao) {
        return NextResponse.json(
          {
            success: false,
            error: "Bolão não encontrado",
          },
          { status: 404 }
        )
      }

      // Atualizar dados do bolão com retry
      let updateSuccess = false
      let retryCount = 0
      const maxRetries = 3

      while (!updateSuccess && retryCount < maxRetries) {
        try {
          await Promise.race([
            executeQuery(`
              UPDATE boloes SET
                nome = ?,
                descricao = ?,
                valor_aposta = ?,
                valor_premium = ?,
                premio_total = ?,
                max_participantes = ?,
                data_inicio = ?,
                data_fim = ?,
                campeonatos_selecionados = ?,
                partidas_selecionadas = ?,
                banner_image = ?
              WHERE id = ?
            `, [
              data.nome,
              data.descricao,
              data.valor_aposta,
              data.valor_premium || (parseFloat(data.valor_aposta) * 2),
              data.premio_total,
              data.max_participantes,
              data.data_inicio,
              data.data_fim,
              JSON.stringify(data.campeonatos_selecionados),
              JSON.stringify(data.partidas_selecionadas),
              data.banner_image || null,
              bolaoId
            ]),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Timeout na atualização completa')), 8000)
            )
          ])
          updateSuccess = true
        } catch (retryError) {
          retryCount++
          console.warn(`⚠️ Tentativa ${retryCount} falhou:`, retryError.message)

          if (retryCount < maxRetries) {
            // Aguardar antes de tentar novamente
            await new Promise(resolve => setTimeout(resolve, 2000))
          } else {
            throw retryError
          }
        }
      }

      console.log(`✅ Bolão ${bolaoId} editado no banco`)
      return NextResponse.json({
        success: true,
        message: "Bolão atualizado com sucesso",
        source: 'database'
      })

    } catch (dbError) {
      console.error(`❌ Erro ao editar bolão no banco:`, dbError.message)

      return NextResponse.json({
        success: false,
        error: 'Erro ao editar bolão no banco de dados',
        message: dbError.message
      }, { status: 500 })
    }
  } catch (error) {
    console.error("Erro ao atualizar bolão:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido",
      },
      { status: 500 }
    )
  }
}

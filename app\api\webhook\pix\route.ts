import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST(request: NextRequest) {
  try {
    await initializeDatabase()
    
    const body = await request.json()
    const { 
      qr_code_payment_id, 
      transaction_id, 
      order_id, 
      amount, 
      description, 
      status, 
      end_to_end_id, 
      last_updated_at, 
      error 
    } = body

    console.log("🔔 Webhook PIX meiodepagamento.com recebido:", { 
      qr_code_payment_id, 
      transaction_id, 
      order_id, 
      amount, 
      status, 
      end_to_end_id 
    })

    // Validações
    if (!order_id && !transaction_id) {
      console.log("❌ Webhook PIX: order_id ou transaction_id obrigatório")
      return NextResponse.json({ 
        qr_code_payment_id: qr_code_payment_id || "",
        transaction_id: transaction_id || "",
        order_id: order_id || "",
        amount: amount || "",
        description: description || "",
        status: "ERROR",
        end_to_end_id: end_to_end_id || "",
        last_updated_at: last_updated_at || new Date().toISOString(),
        error: "order_id ou transaction_id é obrigatório"
      }, { status: 400 })
    }

    // Usar order_id ou transaction_id como identificador
    const paymentId = order_id || transaction_id

    // Mapear status para o banco
    let dbStatus = status
    if (status === 'PAID' || status === 'paid' || status === 'aprovado') {
      dbStatus = 'pago'
    } else if (status === 'PENDING' || status === 'pending' || status === 'pendente') {
      dbStatus = 'pendente'
    } else if (status === 'CANCELLED' || status === 'cancelled' || status === 'cancelado') {
      dbStatus = 'cancelado'
    } else if (status === 'FAILED' || status === 'failed' || status === 'falhou') {
      dbStatus = 'cancelado'
    }

    console.log("💾 Processando webhook:", { 
      paymentId, 
      statusOriginal: status, 
      statusBanco: dbStatus,
      amount: amount 
    })

    try {
      // Buscar bilhete pelo código ou valor
      let bilhete = []
      
      // Primeiro tentar buscar pelo código exato
      if (paymentId) {
        bilhete = await executeQuery(`
          SELECT * FROM bilhetes 
          WHERE codigo = ? 
          LIMIT 1
        `, [paymentId])
      }

      // Se não encontrou, buscar por valor e status pendente (mais recente)
      if (bilhete.length === 0 && amount) {
        const valorDecimal = parseFloat(amount)
        console.log("🔍 Buscando bilhete por valor:", valorDecimal)
        
        bilhete = await executeQuery(`
          SELECT * FROM bilhetes 
          WHERE valor_total = ? 
          AND status = 'pendente'
          AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
          ORDER BY created_at DESC
          LIMIT 1
        `, [valorDecimal])
      }

      if (bilhete.length > 0) {
        const bilheteEncontrado = bilhete[0]
        console.log("✅ Bilhete encontrado:", {
          id: bilheteEncontrado.id,
          codigo: bilheteEncontrado.codigo,
          valor: bilheteEncontrado.valor_total,
          status_atual: bilheteEncontrado.status
        })

        // Atualizar status do bilhete
        const updateResult = await executeQuery(`
          UPDATE bilhetes 
          SET status = ?, updated_at = NOW() 
          WHERE id = ?
        `, [dbStatus, bilheteEncontrado.id])

        console.log("✅ Bilhete atualizado:", updateResult)

        // Resposta de sucesso no formato esperado pela API meiodepagamento.com
        return NextResponse.json({
          qr_code_payment_id: qr_code_payment_id || "",
          transaction_id: transaction_id || "",
          order_id: order_id || "",
          amount: amount || "",
          description: description || "",
          status: status,
          end_to_end_id: end_to_end_id || "",
          last_updated_at: last_updated_at || new Date().toISOString(),
          error: error || ""
        })
      } else {
        console.log("⚠️ Bilhete não encontrado para:", { paymentId, amount })
        
        // Buscar bilhetes recentes para debug
        const bilhetesRecentes = await executeQuery(`
          SELECT id, codigo, valor_total, status, created_at 
          FROM bilhetes 
          WHERE status = 'pendente'
          ORDER BY created_at DESC 
          LIMIT 5
        `)
        
        console.log("📋 Bilhetes pendentes recentes:", bilhetesRecentes)
        
        // Retorna sucesso mesmo sem encontrar para não reenviar webhook
        return NextResponse.json({
          qr_code_payment_id: qr_code_payment_id || "",
          transaction_id: transaction_id || "",
          order_id: order_id || "",
          amount: amount || "",
          description: description || "",
          status: status,
          end_to_end_id: end_to_end_id || "",
          last_updated_at: last_updated_at || new Date().toISOString(),
          error: error || "Bilhete não encontrado"
        })
      }

    } catch (dbError) {
      console.error("❌ Erro ao atualizar banco:", dbError)
      
      return NextResponse.json({
        qr_code_payment_id: qr_code_payment_id || "",
        transaction_id: transaction_id || "",
        order_id: order_id || "",
        amount: amount || "",
        description: description || "",
        status: "ERROR",
        end_to_end_id: end_to_end_id || "",
        last_updated_at: last_updated_at || new Date().toISOString(),
        error: "Database error"
      }, { status: 500 })
    }

  } catch (error) {
    console.error("❌ Erro geral no webhook:", error)
    
    return NextResponse.json({
      qr_code_payment_id: "",
      transaction_id: "",
      order_id: "",
      amount: "",
      description: "",
      status: "ERROR",
      end_to_end_id: "",
      last_updated_at: new Date().toISOString(),
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

// Método GET para testar se o endpoint está funcionando
export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: "Webhook PIX meiodepagamento.com endpoint is working",
    endpoint: "/api/webhook/pix",
    method: "POST",
    expected_payload: {
      qr_code_payment_id: "string",
      transaction_id: "string", 
      order_id: "string",
      amount: "string",
      description: "string",
      status: "PAID|PENDING|CANCELLED|FAILED",
      end_to_end_id: "string",
      last_updated_at: "string",
      error: "string"
    },
    example: {
      qr_code_payment_id: "qr123456",
      transaction_id: "txn789012",
      order_id: "order345678",
      amount: "1.00",
      description: "Pagamento de aposta",
      status: "PAID",
      end_to_end_id: "E12345678901234567890123456789012",
      last_updated_at: "2025-06-22T00:00:00Z",
      error: ""
    },
    note: "Este endpoint recebe webhooks da API meiodepagamento.com conforme documentação oficial"
  })
}

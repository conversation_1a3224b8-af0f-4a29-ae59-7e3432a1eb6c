import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"
import { protectTeamLogos } from "@/lib/teams-display-only"

export async function GET() {
  try {
    console.log("🔄 Iniciando busca de bolões...")

    // Tentar buscar dados reais, com fallback para mockados
    try {
      console.log("🔄 Tentando buscar bolões reais do banco...")

      // 🛡️ PROTEÇÃO: Logos dos times são SOMENTE LEITURA
      try {
        protectTeamLogos()
      } catch (protectError) {
        console.warn("⚠️ Erro na proteção de logos:", protectError.message)
      }

      await initializeDatabase()

      // Buscar bolões com timeout
      const boloes = await Promise.race([
        executeQuery(`
          SELECT
            b.*,
            u.nome as criado_por_nome
          FROM boloes b
          LEFT JOIN usuarios u ON b.criado_por = u.id
          WHERE b.status = 'ativo'
          ORDER BY b.data_criacao DESC
          LIMIT 10
        `),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout na consulta')), 5000)
        )
      ])

      console.log(`✅ ${boloes.length} bolões encontrados no banco`)

      // Para cada bolão, processar os jogos das partidas selecionadas
      const boloesComJogos = await Promise.all(boloes.map(async (bolao: any) => {
        let jogos = []

        try {
          // Se o bolão tem partidas selecionadas, processá-las
          if (bolao.partidas_selecionadas) {
            const partidasIds = typeof bolao.partidas_selecionadas === 'string'
              ? JSON.parse(bolao.partidas_selecionadas)
              : bolao.partidas_selecionadas

            if (Array.isArray(partidasIds) && partidasIds.length > 0) {
              // As partidas já vêm com todos os dados necessários
              jogos = partidasIds.map((partida: any) => ({
                id: partida.id,
                campeonato_nome: partida.campeonato_nome,
                campeonato_logo: partida.campeonato_logo,
                campeonato_id: partida.campeonato_id,
                time_casa_nome: partida.time_casa_nome,
                time_casa_logo: partida.time_casa_logo,
                time_fora_nome: partida.time_fora_nome,
                time_fora_logo: partida.time_fora_logo,
                data_jogo: partida.data_jogo,
                status: partida.status,
                matchday: partida.matchday
              }))

              console.log(`🎮 Bolão ${bolao.id}: ${jogos.length} jogos processados das partidas selecionadas`)
            }
          }
        } catch (error) {
          console.error(`❌ Erro ao processar jogos do bolão ${bolao.id}:`, error)
          jogos = []
        }

        return {
          ...bolao,
          jogos: jogos
        }
      }))

      console.log(`✅ Retornando ${boloesComJogos.length} bolões com jogos processados`)

      return NextResponse.json({
        success: true,
        boloes: boloesComJogos || [],
        source: 'database'
      })

    } catch (dbError) {
      console.warn("⚠️ Erro ao acessar banco, usando dados mockados:", dbError.message)

      // Erro ao acessar banco de dados

      return NextResponse.json({
        success: true,
        boloes: [],
        message: "Erro ao acessar banco de dados"
      })
    }

    // Código original comentado temporariamente
    /*
    // 🛡️ PROTEÇÃO: Logos dos times são SOMENTE LEITURA
    try {
      protectTeamLogos()
    } catch (protectError) {
      console.warn("⚠️ Erro na proteção de logos:", protectError.message)
    }

    console.log("🔄 Inicializando banco de dados...")
    await initializeDatabase()

    // Código original comentado temporariamente devido a problemas de conexão MySQL
    /*
    // Primeiro, verificar se há bolões na tabela
    console.log("🔄 Verificando total de bolões...")
    const totalBoloes = await executeQuery(`SELECT COUNT(*) as total FROM boloes`)
    console.log("🔍 Total de bolões na tabela:", totalBoloes[0]?.total || 0)

    // Buscar bolões ativos
    console.log("🔍 Buscando bolões ativos...")
    const boloes = await executeQuery(\`
      SELECT
        b.*,
        u.nome as criado_por_nome
      FROM boloes b
      LEFT JOIN usuarios u ON b.criado_por = u.id
      WHERE b.status = 'ativo'
      ORDER BY b.data_criacao DESC
    \`)
    console.log("✅ Query de bolões executada com sucesso")

    console.log(\`📊 Encontrados \${boloes.length} bolões:\`, boloes.map(b => ({ id: b.id, nome: b.nome, status: b.status })))

    // Para cada bolão, buscar os jogos reais das partidas selecionadas
    const boloesComJogos = await Promise.all(boloes.map(async (bolao) => {
      let jogos = []

      try {
        // Tentar buscar jogos reais associados ao bolão através da tabela bolao_jogos (MySQL)
        try {
          const jogosQuery = \`
            SELECT
              j.*,
              c.nome as campeonato_nome,
              c.descricao as campeonato_descricao,
              c.logo_url as campeonato_logo,
              c.pais as campeonato_pais,
              tc.nome as time_casa_nome,
              tc.nome_curto as time_casa_curto,
              tc.logo_url as time_casa_logo,
              tc.pais as time_casa_pais,
              tf.nome as time_fora_nome,
              tf.nome_curto as time_fora_curto,
              tf.logo_url as time_fora_logo,
              tf.pais as time_fora_pais
            FROM bolao_jogos bj
            JOIN jogos j ON bj.jogo_id = j.id
            LEFT JOIN campeonatos c ON j.campeonato_id = c.id
            LEFT JOIN times tc ON j.time_casa_id = tc.id
            LEFT JOIN times tf ON j.time_fora_id = tf.id
            WHERE bj.bolao_id = ?
            ORDER BY j.data_jogo ASC
          \`

          jogos = await executeQuery(jogosQuery, [bolao.id])
        } catch (error) {
          console.log(\`⚠️ Erro ao buscar jogos via bolao_jogos (MySQL): \${error.message}\`)
          jogos = []
        }

        // Se não encontrou jogos na tabela bolao_jogos, tentar buscar pelas partidas_selecionadas
        if (jogos.length === 0 && bolao.partidas_selecionadas) {
          const partidasIds = typeof bolao.partidas_selecionadas === 'string'
            ? JSON.parse(bolao.partidas_selecionadas)
            : bolao.partidas_selecionadas

          if (Array.isArray(partidasIds) && partidasIds.length > 0) {
            // Extrair apenas os IDs se são objetos
            const ids = partidasIds.map(p => typeof p === 'object' && p !== null ? p.id : p).filter(id => id)

            if (ids.length > 0) {
              const placeholders = ids.map(() => '?').join(',')

              // Buscar jogos no MySQL
              try {
                const jogosAlternativosQuery = \`
                  SELECT
                    j.*,
                    c.nome as campeonato_nome,
                    c.descricao as campeonato_descricao,
                    c.logo_url as campeonato_logo,
                    c.pais as campeonato_pais,
                    tc.nome as time_casa_nome,
                    tc.nome_curto as time_casa_curto,
                    tc.logo_url as time_casa_logo,
                    tc.pais as time_casa_pais,
                    tf.nome as time_fora_nome,
                    tf.nome_curto as time_fora_curto,
                    tf.logo_url as time_fora_logo,
                    tf.pais as time_fora_pais
                  FROM jogos j
                  LEFT JOIN campeonatos c ON j.campeonato_id = c.id
                  LEFT JOIN times tc ON j.time_casa_id = tc.id
                  LEFT JOIN times tf ON j.time_fora_id = tf.id
                  WHERE j.id IN (\${placeholders})
                  ORDER BY j.data_jogo ASC
                \`

                jogos = await executeQuery(jogosAlternativosQuery, ids)
                console.log(\`🎮 Bolão \${bolao.id}: Encontrados \${jogos.length} jogos no MySQL\`)
              } catch (mysqlError) {
                console.log(\`⚠️ Erro ao buscar jogos no MySQL: \${mysqlError.message}\`)
                jogos = []
              }
            }
          }
        }

        console.log(\`🎮 Bolão \${bolao.id} (\${bolao.nome}): \${jogos.length} jogos encontrados\`)

      } catch (error) {
        console.error(\`Erro ao buscar jogos do bolão \${bolao.id}:\`, error)
        jogos = []
      }

      return {
        ...bolao,
        jogos: jogos
      }
    }))

    console.log(\`✅ Retornando \${boloesComJogos.length} bolões com jogos\`)

    return NextResponse.json({
      success: true,
      boloes: boloesComJogos || []
    })
    */
  } catch (error) {
    console.error("❌ ERRO DETALHADO ao buscar bolões:")
    console.error("Tipo do erro:", typeof error)
    console.error("Nome do erro:", error.name)
    console.error("Mensagem:", error.message)
    console.error("Stack:", error.stack)

    return NextResponse.json(
      {
        success: false,
        error: `Erro interno do servidor: ${error.message}`,
        details: error.stack,
        boloes: []
      },
      { status: 500 }
    )
  }
}

# Script PowerShell para reiniciar o servidor Next.js

Write-Host "🔄 Reiniciando servidor Next.js..." -ForegroundColor Cyan

# Parar processos Node.js existentes
Write-Host "🛑 Parando processos Node.js..." -ForegroundColor Yellow
Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
Get-Process -Name "next" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue

# Aguardar um pouco para garantir que os processos foram finalizados
Start-Sleep -Seconds 2

# Limpar cache do Next.js
Write-Host "🧹 Limpando cache..." -ForegroundColor Yellow
if (Test-Path ".next") {
    Remove-Item -Recurse -Force ".next" -ErrorAction SilentlyContinue
}
if (Test-Path "node_modules\.cache") {
    Remove-Item -Recurse -Force "node_modules\.cache" -ErrorAction SilentlyContinue
}

# Verificar se deve fazer instalação completa
if ($args[0] -eq "--fresh") {
    Write-Host "🗑️ Removendo node_modules e package-lock.json..." -ForegroundColor Yellow
    if (Test-Path "node_modules") {
        Remove-Item -Recurse -Force "node_modules" -ErrorAction SilentlyContinue
    }
    if (Test-Path "package-lock.json") {
        Remove-Item -Force "package-lock.json" -ErrorAction SilentlyContinue
    }
    
    Write-Host "📦 Instalando dependências..." -ForegroundColor Yellow
    npm install
}

# Iniciar servidor
Write-Host "🚀 Iniciando servidor..." -ForegroundColor Green
npm run dev

Write-Host "✅ Servidor reiniciado!" -ForegroundColor Green

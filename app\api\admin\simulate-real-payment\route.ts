import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST(request: Request) {
  try {
    await initializeDatabase()

    console.log("💰 Simulando pagamento real de R$ 1,00...")

    // Buscar bilhete mais recente de R$ 1,00 com status pendente
    const bilhetesPendentes = await executeQuery(`
      SELECT * FROM bilhetes 
      WHERE valor_total = 1.00 
      AND status = 'pendente'
      AND created_at >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
      ORDER BY created_at DESC
      LIMIT 1
    `)

    if (bilhetesPendentes.length === 0) {
      // Criar um bilhete se não houver nenhum pendente
      const timestamp = Date.now()
      const codigo = `BLT${timestamp}REAL`

      await executeQuery(`
        INSERT INTO bilhetes (
          codigo, 
          usuario_id, 
          usuario_nome, 
          usuario_email, 
          usuario_cpf, 
          valor_total, 
          quantidade_apostas, 
          status, 
          created_at, 
          updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        codigo,
        null,
        "Pagamento Real PIX",
        "<EMAIL>",
        "000.000.000-00",
        1.00,
        11,
        "pendente"
      ])

      const bilheteCriado = await executeQuery(`
        SELECT * FROM bilhetes WHERE codigo = ?
      `, [codigo])

      console.log("🎫 Bilhete criado para simular pagamento real:", bilheteCriado[0])
      
      // Simular webhook da meiodepagamento.com
      const webhookPayload = {
        qr_code_payment_id: "qr_real_payment_123",
        transaction_id: "E22896431202506220125Ks7tfFQ5JUh",
        order_id: codigo,
        amount: "1.00",
        description: "Pagamento real de aposta",
        status: "PAID",
        end_to_end_id: "E22896431202506220125Ks7tfFQ5JUh",
        last_updated_at: new Date().toISOString(),
        error: ""
      }

      // Chamar o webhook internamente
      const webhookResponse = await fetch('http://localhost:3000/api/webhook/pix', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(webhookPayload)
      })

      const webhookResult = await webhookResponse.json()

      return NextResponse.json({
        success: true,
        message: "Pagamento real simulado com sucesso!",
        bilhete_criado: bilheteCriado[0],
        webhook_payload: webhookPayload,
        webhook_response: webhookResult,
        action: "created_and_paid"
      })

    } else {
      const bilhete = bilhetesPendentes[0]
      console.log("🎫 Bilhete pendente encontrado:", bilhete)

      // Simular webhook da meiodepagamento.com
      const webhookPayload = {
        qr_code_payment_id: "qr_real_payment_123",
        transaction_id: "E22896431202506220125Ks7tfFQ5JUh",
        order_id: bilhete.codigo,
        amount: "1.00",
        description: "Pagamento real de aposta",
        status: "PAID",
        end_to_end_id: "E22896431202506220125Ks7tfFQ5JUh",
        last_updated_at: new Date().toISOString(),
        error: ""
      }

      // Chamar o webhook internamente
      const webhookResponse = await fetch('http://localhost:3000/api/webhook/pix', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(webhookPayload)
      })

      const webhookResult = await webhookResponse.json()

      // Buscar bilhete atualizado
      const bilheteAtualizado = await executeQuery(`
        SELECT * FROM bilhetes WHERE id = ?
      `, [bilhete.id])

      return NextResponse.json({
        success: true,
        message: "Pagamento real processado com sucesso!",
        bilhete_original: bilhete,
        bilhete_atualizado: bilheteAtualizado[0],
        webhook_payload: webhookPayload,
        webhook_response: webhookResult,
        action: "updated_existing"
      })
    }

  } catch (error) {
    console.error("❌ Erro ao simular pagamento real:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { initializeDatabase, executeQuery } from '@/lib/database-config'

export async function POST(request: NextRequest) {
  try {
    console.log('🔧 Iniciando migração para adicionar coluna valor_premium...')
    
    await initializeDatabase()

    // Verificar se a coluna já existe
    const columnExists = await executeQuery(`
      SELECT COUNT(*) as count
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_SCHEMA = DATABASE()
      AND TABLE_NAME = 'boloes'
      AND COLUMN_NAME = 'valor_premium'
    `)

    const exists = columnExists[0]?.count > 0

    if (!exists) {
      console.log('➕ Adicionando coluna valor_premium...')
      
      // Adicionar coluna valor_premium
      await executeQuery(`
        ALTER TABLE boloes 
        ADD COLUMN valor_premium DECIMAL(10,2) DEFAULT 0.00 
        AFTER valor_aposta
      `)

      console.log('✅ Coluna valor_premium adicionada com sucesso')
    } else {
      console.log('ℹ️ Coluna valor_premium já existe')
    }

    // Atualizar registros existentes
    console.log('🔄 Atualizando registros existentes...')
    
    const updateResult = await executeQuery(`
      UPDATE boloes 
      SET valor_premium = valor_aposta * 2 
      WHERE valor_premium = 0.00 OR valor_premium IS NULL
    `)

    console.log(`✅ ${updateResult.affectedRows || 0} registros atualizados`)

    // Verificar resultado
    const sampleData = await executeQuery(`
      SELECT 
        nome,
        valor_aposta,
        valor_premium,
        ROUND(valor_premium / valor_aposta, 2) as multiplicador
      FROM boloes 
      LIMIT 5
    `)

    return NextResponse.json({
      success: true,
      message: 'Migração concluída com sucesso',
      columnAdded: !exists,
      recordsUpdated: updateResult.affectedRows || 0,
      sampleData: sampleData
    })

  } catch (error) {
    console.error('❌ Erro na migração:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Erro na migração',
      message: error.message
    }, { status: 500 })
  }
}

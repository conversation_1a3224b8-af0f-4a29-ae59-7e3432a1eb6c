import { executeQuery, initializeDatabase } from '../lib/database-config.js'

async function createTestUser() {
  try {
    console.log('🔧 Inicializando conexão com o banco...')
    await initializeDatabase()

    console.log('👥 Verificando se usuário ID 18 existe...')
    const existingUser = await executeQuery('SELECT id, nome, email FROM usuarios WHERE id = ?', [18])
    
    if (existingUser.length > 0) {
      console.log('✅ Usuário ID 18 já existe:', existingUser[0])
      return
    }

    console.log('⚠️ Usuário ID 18 não existe. Criando usuário de teste...')
    
    // Criar usuário com ID específico
    await executeQuery('SET foreign_key_checks = 0')
    
    const result = await executeQuery(`
      INSERT INTO usuarios (id, nome, email, telefone, cpf_cnpj, senha_hash, tipo, status, saldo, data_cadastro)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `, [
      18,
      'Usu<PERSON>rio Teste',
      '<EMAIL>',
      '11999999999',
      '12345678901',
      '$2b$10$defaulthash',
      'usuario',
      'ativo',
      0.00
    ])
    
    await executeQuery('SET foreign_key_checks = 1')
    
    console.log('✅ Usuário de teste criado com ID 18')
    
    // Verificar se foi criado corretamente
    const newUser = await executeQuery('SELECT id, nome, email, status FROM usuarios WHERE id = ?', [18])
    console.log('📋 Usuário criado:', newUser[0])

  } catch (error) {
    console.error('❌ Erro ao criar usuário de teste:', error)
    process.exit(1)
  }
}

createTestUser()
